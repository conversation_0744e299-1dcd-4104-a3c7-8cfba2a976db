/**
 * Attempts to repair a JSON string by removing trailing commas and extracting a valid object.
 */
function jsonRepair(text) {
    // This function finds the first '{' and the last '}' and extracts the content between them.
    const firstBracket = text.indexOf('{');
    const lastBracket = text.lastIndexOf('}');

    if (firstBracket === -1 || lastBracket === -1 || lastBracket < firstBracket) {
        return text; // Return original text if no valid JSON object is found
    }

    let jsonString = text.substring(firstBracket, lastBracket + 1);

    // Remove trailing commas
    jsonString = jsonString.replace(/,\s*([\]}])/g, '$1');

    return jsonString;
}

function unflattenObject(data) {
    "use strict";
    if (Object.prototype.toString.call(data) !== '[object Object]') {
        return data;
    }
    const result = {};
    for (const i in data) {
        const keys = i.split('.');
        keys.reduce((r, e, j) => {
            if (r[e]) {
                return r[e];
            }
            if (j === keys.length - 1) {
                return r[e] = data[i];
            }
            return r[e] = {};
        }, result);
    }
    return result;
}


/**
 * Executes the network request using the Fetch API with enhanced progress tracking.
 */
async function executeRequest(requestData, progressCallback = null) {
    const { method, url, headers, body } = requestData;
    const fetchOptions = {
        method: method,
        headers: new Headers(),
    };

    if (headers) {
        headers.forEach(header => {
            if (header.key && header.value) {
                fetchOptions.headers.append(header.key, header.value);
            }
        });
    }

    // Estimate request size for progress tracking
    let requestSize = 0;
    let hasLargeFile = false;

    if (method !== 'GET' && method !== 'DELETE') {
        // If the body is a FormData object, let the browser handle the Content-Type.
        if (body instanceof FormData) {
            fetchOptions.body = body;
            // IMPORTANT: Delete the Content-Type header. The browser needs to set it
            // with the correct boundary for multipart/form-data.
            fetchOptions.headers.delete('Content-Type');

            // Estimate FormData size for progress indication
            for (let pair of body.entries()) {
                if (pair[1] instanceof File || pair[1] instanceof Blob) {
                    requestSize += pair[1].size;
                    if (pair[1].size > 5 * 1024 * 1024) { // 5MB threshold for large file detection
                        hasLargeFile = true;
                    }
                }
            }
        } else {
            fetchOptions.body = body;
            if (!fetchOptions.headers.has('Content-Type')) {
                fetchOptions.headers.append('Content-Type', 'application/json');
            }
            requestSize = new Blob([body]).size;
        }
    }

    try {
        // Show initial progress for large files
        if (hasLargeFile && progressCallback) {
            progressCallback({
                stage: 'uploading',
                progress: 0,
                message: `Starting upload... (${(requestSize / 1024 / 1024).toFixed(1)} MB)`,
                fileSize: requestSize
            });
        }

        // Simulate progress updates for large files with more realistic timing
        let progressInterval;
        if (hasLargeFile && progressCallback) {
            let currentProgress = 0;
            const startTime = Date.now();
            const estimatedDuration = Math.min(Math.max(requestSize / (1024 * 1024) * 2, 5), 30); // 2 seconds per MB, min 5s, max 30s

            progressInterval = setInterval(() => {
                const elapsed = (Date.now() - startTime) / 1000;
                const expectedProgress = (elapsed / estimatedDuration) * 85; // Cap at 85% until completion

                // Smooth progress with some randomness
                currentProgress = Math.min(currentProgress + Math.random() * 8 + 2, expectedProgress);

                if (currentProgress > 85) currentProgress = 85;

                progressCallback({
                    stage: 'uploading',
                    progress: Math.round(currentProgress),
                    message: `Uploading... ${Math.round(currentProgress)}% (${(requestSize / 1024 / 1024).toFixed(1)} MB)`,
                    fileSize: requestSize
                });
            }, 500); // Update every 500ms for smoother progress
        }

        const response = await fetch(url, fetchOptions);

        // Clear progress interval
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // Update progress for response processing
        if (hasLargeFile && progressCallback) {
            progressCallback({
                stage: 'processing',
                progress: 95,
                message: 'Processing response...',
                fileSize: requestSize
            });
        }

        const responseBody = await response.text();

        if (hasLargeFile && progressCallback) {
            progressCallback({
                stage: 'complete',
                progress: 100,
                message: `Upload completed successfully! (${(requestSize / 1024 / 1024).toFixed(1)} MB)`,
                fileSize: requestSize
            });
        }

        return {
            status: response.status,
            statusText: response.statusText,
            body: responseBody,
            isSuccess: response.ok, // Use the default 'ok' property
            requestSize: requestSize,
            hasLargeFile: hasLargeFile
        };
    } catch (error) {
        // Clear progress interval on error
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        if (hasLargeFile && progressCallback) {
            progressCallback({
                stage: 'error',
                progress: 0,
                message: `Upload failed: ${error.message}`,
                error: error.message,
                fileSize: requestSize
            });
        }

        return {
            error: error.message,
            isSuccess: false,
            requestSize: requestSize,
            hasLargeFile: hasLargeFile
        };
    }
}
