<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Storage Usage Analysis - Multi Webhook Sender</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            height: auto;
            max-width: 1000px;
            margin: 0 auto;
            background-color: var(--background-color);
        }

        .analysis-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--panel-background);
        }

        .analysis-section h2 {
            margin-top: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .storage-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .storage-stat {
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--input-background);
            text-align: center;
        }

        .storage-stat .value {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .storage-stat .label {
            font-size: 14px;
            color: var(--light-text-color);
        }

        .progress-bar {
            width: 100%;
            height: 30px;
            background-color: var(--input-background);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            overflow: hidden;
            margin: 15px 0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .progress-fill.normal {
            background-color: var(--success-color);
        }

        .progress-fill.warning {
            background-color: #f39c12;
        }

        .progress-fill.critical {
            background-color: var(--error-color);
        }

        .storage-items {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin: 15px 0;
        }

        .storage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            background-color: var(--panel-background);
        }

        .storage-item:last-child {
            border-bottom: none;
        }

        .storage-item:hover {
            background-color: var(--input-background);
        }

        .item-info {
            flex-grow: 1;
        }

        .item-key {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 3px;
        }

        .item-details {
            font-size: 12px;
            color: var(--light-text-color);
        }

        .item-size {
            font-weight: bold;
            color: var(--primary-color);
            margin-left: 15px;
        }

        .cleanup-actions {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }

        .action-btn {
            padding: 10px 15px;
            border: 1px solid var(--primary-color);
            background-color: var(--primary-color);
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .action-btn.danger {
            border-color: var(--error-color);
            background-color: var(--error-color);
        }

        .action-btn.success {
            border-color: var(--success-color);
            background-color: var(--success-color);
        }

        .analysis-results {
            background-color: rgba(74, 144, 226, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .recommendations {
            background-color: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .warning-box {
            background-color: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--error-color);
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: var(--light-text-color);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <h1>🔍 Chrome Storage Usage Analysis</h1>
    <p>Comprehensive analysis of Multi Webhook Sender storage consumption and optimization recommendations.</p>

    <!-- Storage Overview -->
    <div class="analysis-section">
        <h2>📊 Storage Overview</h2>

        <div class="storage-overview">
            <div class="storage-stat">
                <div class="value" id="total-used">--</div>
                <div class="label">Total Used</div>
            </div>
            <div class="storage-stat">
                <div class="value" id="available-space">--</div>
                <div class="label">Available Space</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="usage-progress" style="width: 0%;">
                0% Used
            </div>
        </div>

        <div class="cleanup-actions">
            <button class="action-btn" id="analyze-btn">🔍 Analyze Storage</button>
            <button class="action-btn success" id="cleanup-temp-btn">🧹 Clean Temp Files</button>
            <button class="action-btn danger" id="emergency-cleanup-btn">🚨 Emergency Cleanup</button>
        </div>
    </div>

    <!-- Storage Items Breakdown -->
    <div class="analysis-section">
        <h2>📋 Storage Items Breakdown</h2>

        <div id="loading-indicator" class="loading">
            <div class="spinner"></div>
            <span>Analyzing storage items...</span>
        </div>

        <div id="storage-items-container" class="storage-items" style="display: none;">
            <!-- Storage items will be populated here -->
        </div>
    </div>

    <!-- Analysis Results -->
    <div class="analysis-section">
        <h2>📈 Analysis Results</h2>

        <div id="analysis-output" class="analysis-results">
            Ready to analyze storage usage. Click "Analyze Storage" button above to begin...
        </div>
    </div>

    <!-- Recommendations -->
    <div class="analysis-section">
        <h2>💡 Optimization Recommendations</h2>

        <div id="recommendations-container" class="recommendations">
            <h4>🎯 Smart Storage Management Tips:</h4>
            <ul>
                <li><strong>Regular Cleanup:</strong> Use the cleanup button weekly for large file users</li>
                <li><strong>Monitor Usage:</strong> Keep storage under 8MB to avoid quota issues</li>
                <li><strong>Large Files:</strong> Send large files immediately after upload (don't save)</li>
                <li><strong>Failed Uploads:</strong> Clean storage after any upload failures</li>
                <li><strong>Browser Crashes:</strong> Run cleanup after unexpected browser shutdowns</li>
            </ul>
        </div>
    </div>

    <script>
        let storageData = {};
        let analysisComplete = false;

        // Utility functions
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function getStorageItemType(key) {
            if (key.startsWith('temp_files_')) return 'Temporary File';
            if (key === 'requests') return 'Saved Requests';
            if (key === 'settings') return 'Settings';
            if (key === 'theme') return 'Theme';
            if (key === 'uploadState') return 'Upload State';
            if (key === 'sortBy') return 'Sort Preferences';
            if (key === 'panelLeftWidth') return 'UI Layout';
            if (key === 'fullScreenMode') return 'UI Preferences';
            if (key.includes('temp_') || key.includes('upload_')) return 'Temporary Data';
            return 'Extension Data';
        }

        function getStorageItemAge(key) {
            if (key.startsWith('temp_files_')) {
                const timestamp = parseInt(key.replace('temp_files_', ''));
                if (!isNaN(timestamp)) {
                    const ageMs = Date.now() - timestamp;
                    const ageMinutes = Math.floor(ageMs / (1000 * 60));
                    const ageHours = Math.floor(ageMinutes / 60);
                    const ageDays = Math.floor(ageHours / 24);

                    if (ageDays > 0) return `${ageDays} day(s) old`;
                    if (ageHours > 0) return `${ageHours} hour(s) old`;
                    return `${ageMinutes} minute(s) old`;
                }
            }
            return 'Unknown age';
        }

        async function analyzeStorage() {
            const loadingIndicator = document.getElementById('loading-indicator');
            const storageItemsContainer = document.getElementById('storage-items-container');
            const analysisOutput = document.getElementById('analysis-output');

            loadingIndicator.style.display = 'flex';
            storageItemsContainer.style.display = 'none';

            try {
                // Get storage usage stats
                const bytesInUse = await chrome.storage.local.getBytesInUse();
                const maxBytes = chrome.storage.local.QUOTA_BYTES || 10 * 1024 * 1024;
                const availableBytes = maxBytes - bytesInUse;
                const usagePercentage = (bytesInUse / maxBytes) * 100;

                // Get all storage items
                const allItems = await chrome.storage.local.get(null);
                storageData = allItems;

                // Update overview
                document.getElementById('total-used').textContent = formatBytes(bytesInUse);
                document.getElementById('available-space').textContent = formatBytes(availableBytes);

                // Update progress bar
                const progressFill = document.getElementById('usage-progress');
                progressFill.style.width = usagePercentage + '%';
                progressFill.textContent = `${usagePercentage.toFixed(1)}% Used`;

                if (usagePercentage > 90) {
                    progressFill.className = 'progress-fill critical';
                } else if (usagePercentage > 75) {
                    progressFill.className = 'progress-fill warning';
                } else {
                    progressFill.className = 'progress-fill normal';
                }

                // Analyze items
                let tempFileCount = 0;
                let tempFileSize = 0;
                let oldTempFiles = 0;
                let largeItems = [];
                let suspiciousItems = [];

                const storageBreakdown = [];

                for (const [key, value] of Object.entries(allItems)) {
                    const itemSize = JSON.stringify(value).length;
                    const itemType = getStorageItemType(key);
                    const itemAge = getStorageItemAge(key);

                    storageBreakdown.push({
                        key,
                        size: itemSize,
                        type: itemType,
                        age: itemAge,
                        value
                    });

                    // Track temporary files
                    if (key.startsWith('temp_files_')) {
                        tempFileCount++;
                        tempFileSize += itemSize;

                        const timestamp = parseInt(key.replace('temp_files_', ''));
                        if (!isNaN(timestamp)) {
                            const ageMs = Date.now() - timestamp;
                            if (ageMs > 30 * 60 * 1000) { // Older than 30 minutes
                                oldTempFiles++;
                            }
                        }
                    }

                    // Track large items
                    if (itemSize > 100 * 1024) { // Larger than 100KB
                        largeItems.push({ key, size: itemSize, type: itemType });
                    }

                    // Track suspicious items
                    if (key.length > 20 && !['requests', 'settings', 'theme', 'sortBy', 'panelLeftWidth', 'fullScreenMode'].includes(key)) {
                        suspiciousItems.push({ key, size: itemSize, type: itemType });
                    }
                }

                // Sort by size (largest first)
                storageBreakdown.sort((a, b) => b.size - a.size);

                // Populate storage items list
                storageItemsContainer.innerHTML = '';
                storageBreakdown.forEach(item => {
                    const itemEl = document.createElement('div');
                    itemEl.className = 'storage-item';

                    let sizeClass = '';
                    if (item.size > 500 * 1024) sizeClass = 'critical';
                    else if (item.size > 100 * 1024) sizeClass = 'warning';

                    itemEl.innerHTML = `
                        <div class="item-info">
                            <div class="item-key">${item.key}</div>
                            <div class="item-details">${item.type} • ${item.age}</div>
                        </div>
                        <div class="item-size ${sizeClass}">${formatBytes(item.size)}</div>
                    `;

                    storageItemsContainer.appendChild(itemEl);
                });

                // Generate analysis report
                let analysisReport = `🔍 STORAGE ANALYSIS REPORT
=========================

📊 Usage Statistics:
• Total Storage Used: ${formatBytes(bytesInUse)} / ${formatBytes(maxBytes)} (${usagePercentage.toFixed(1)}%)
• Available Space: ${formatBytes(availableBytes)}
• Total Items: ${Object.keys(allItems).length}

📁 Temporary Files Analysis:
• Temporary Files Found: ${tempFileCount}
• Total Temp File Size: ${formatBytes(tempFileSize)}
• Old Temp Files (>30min): ${oldTempFiles}
• Cleanup Potential: ${formatBytes(tempFileSize)}

⚠️ Large Items (>100KB):
${largeItems.map(item => `• ${item.key}: ${formatBytes(item.size)} (${item.type})`).join('\n')}

🔍 Suspicious Items:
${suspiciousItems.map(item => `• ${item.key}: ${formatBytes(item.size)} (${item.type})`).join('\n')}

💡 Storage Health:`;

                if (usagePercentage > 90) {
                    analysisReport += `\n🚨 CRITICAL: Storage usage is dangerously high (${usagePercentage.toFixed(1)}%)
   - Immediate cleanup required to prevent quota errors
   - Use Emergency Cleanup button below`;
                } else if (usagePercentage > 75) {
                    analysisReport += `\n⚠️ WARNING: Storage usage is high (${usagePercentage.toFixed(1)}%)
   - Consider cleaning temporary files soon
   - Monitor large file uploads carefully`;
                } else if (usagePercentage > 50) {
                    analysisReport += `\n📊 MODERATE: Storage usage is moderate (${usagePercentage.toFixed(1)}%)
   - Regular cleanup recommended
   - Monitor for temporary file accumulation`;
                } else {
                    analysisReport += `\n✅ GOOD: Storage usage is healthy (${usagePercentage.toFixed(1)}%)
   - No immediate action required
   - Continue normal usage`;
                }

                if (tempFileCount > 0) {
                    analysisReport += `\n\n🧹 Cleanup Recommendations:
• ${tempFileCount} temporary files can be safely removed
• Potential space savings: ${formatBytes(tempFileSize)}
• ${oldTempFiles} files are older than 30 minutes and should be cleaned`;
                }

                analysisOutput.textContent = analysisReport;
                analysisComplete = true;

                // Update recommendations based on analysis
                updateRecommendations(usagePercentage, tempFileCount, largeItems.length);

            } catch (error) {
                analysisOutput.textContent = `❌ Analysis Error: ${error.message}\n\nThis may occur if the extension doesn't have proper permissions or if storage is inaccessible.`;
            } finally {
                loadingIndicator.style.display = 'none';
                storageItemsContainer.style.display = 'block';
            }
        }

        function updateRecommendations(usagePercentage, tempFileCount, largeItemCount) {
            const recommendationsContainer = document.getElementById('recommendations-container');

            let recommendations = '<h4>🎯 Personalized Recommendations:</h4><ul>';

            if (usagePercentage > 85) {
                recommendations += '<li><strong>🚨 URGENT:</strong> Run emergency cleanup immediately to prevent upload failures</li>';
                recommendations += '<li><strong>📊 Monitor:</strong> Check storage daily until usage drops below 75%</li>';
            }

            if (tempFileCount > 5) {
                recommendations += `<li><strong>🧹 Cleanup:</strong> ${tempFileCount} temporary files detected - use cleanup button</li>`;
            }

            if (largeItemCount > 3) {
                recommendations += '<li><strong>📁 Large Files:</strong> Consider sending large files immediately instead of saving</li>';
            }

            if (usagePercentage > 50) {
                recommendations += '<li><strong>⏰ Schedule:</strong> Set up weekly storage cleanup routine</li>';
            }

            recommendations += '<li><strong>📈 Best Practice:</strong> Keep storage under 8MB for optimal performance</li>';
            recommendations += '<li><strong>🔄 Auto-Cleanup:</strong> The extension automatically cleans files during uploads</li>';
            recommendations += '</ul>';

            recommendationsContainer.innerHTML = recommendations;
        }

        async function cleanupTempFiles() {
            try {
                const allItems = await chrome.storage.local.get(null);
                const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                const currentTime = Date.now();
                let cleanedCount = 0;

                for (const key of tempFileKeys) {
                    const timestampStr = key.replace('temp_files_', '');
                    const timestamp = parseInt(timestampStr, 10);

                    if (!isNaN(timestamp)) {
                        const age = currentTime - timestamp;
                        if (age > 5 * 60 * 1000) { // Older than 5 minutes
                            await chrome.storage.local.remove(key);
                            cleanedCount++;
                        }
                    } else {
                        // Invalid timestamp, clean it up
                        await chrome.storage.local.remove(key);
                        cleanedCount++;
                    }
                }

                alert(`✅ Cleanup completed!\nRemoved ${cleanedCount} temporary files.\n\nClick "Analyze Storage" to see updated results.`);
            } catch (error) {
                alert(`❌ Cleanup failed: ${error.message}`);
            }
        }

        async function emergencyCleanup() {
            if (!confirm('🚨 Emergency Cleanup will remove ALL temporary files regardless of age.\n\nThis action cannot be undone. Continue?')) {
                return;
            }

            try {
                const allItems = await chrome.storage.local.get(null);
                const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                const suspiciousKeys = Object.keys(allItems).filter(key =>
                    key.includes('temp_') ||
                    key.includes('upload_') ||
                    (key.length > 20 && !['requests', 'settings', 'theme', 'sortBy', 'panelLeftWidth', 'fullScreenMode'].includes(key))
                );

                let cleanedCount = 0;

                // Remove all temporary files
                for (const key of tempFileKeys) {
                    await chrome.storage.local.remove(key);
                    cleanedCount++;
                }

                // Remove suspicious keys
                for (const key of suspiciousKeys) {
                    await chrome.storage.local.remove(key);
                    cleanedCount++;
                }

                // Clear upload state
                await chrome.storage.local.remove('uploadState');

                alert(`🆘 Emergency cleanup completed!\nRemoved ${cleanedCount} items.\n\nClick "Analyze Storage" to see updated results.`);
            } catch (error) {
                alert(`❌ Emergency cleanup failed: ${error.message}`);
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('analyze-btn').addEventListener('click', analyzeStorage);
            document.getElementById('cleanup-temp-btn').addEventListener('click', cleanupTempFiles);
            document.getElementById('emergency-cleanup-btn').addEventListener('click', emergencyCleanup);

            // Auto-run analysis on page load
            setTimeout(analyzeStorage, 500);
        });
    </script>
</body>

</html>