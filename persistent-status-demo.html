<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Persistent Upload Status - Multi Webhook Sender</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            height: auto;
            max-width: 900px;
            margin: 0 auto;
            background-color: var(--background-color);
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--panel-background);
        }

        .demo-section h2 {
            margin-top: 0;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .fix-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: auto;
            background-color: rgba(46, 204, 113, 0.2);
            color: var(--success-color);
        }

        .workflow-steps {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .workflow-step {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--input-background);
            transition: all 0.3s ease;
        }

        .workflow-step:hover {
            border-color: var(--primary-color);
            background-color: rgba(74, 144, 226, 0.05);
        }

        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
            font-size: 14px;
            flex-shrink: 0;
        }

        .step-content {
            flex-grow: 1;
        }

        .step-title {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .step-description {
            color: var(--light-text-color);
            font-size: 13px;
            line-height: 1.4;
        }

        .step-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .step-status.before {
            background-color: rgba(231, 76, 60, 0.1);
            color: var(--error-color);
        }

        .step-status.after {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
        }

        .demo-interface {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: var(--background-color);
        }

        .demo-popup {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background-color: var(--panel-background);
            padding: 15px;
        }

        .demo-send-btn {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
            margin-top: 10px;
        }

        .demo-send-btn.normal {
            background-color: var(--primary-color);
            color: white;
        }

        .demo-send-btn.uploading {
            background-color: #f39c12;
            color: white;
        }

        .demo-send-btn.restored {
            background-color: #27ae60;
            color: white;
            animation: restored-glow 2s ease-in-out;
        }

        .demo-spinner {
            width: 14px;
            height: 14px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }

        .progress-percentage {
            font-size: 13px;
            font-weight: 600;
            min-width: 35px;
            text-align: right;
        }

        .technical-details {
            background-color: rgba(74, 144, 226, 0.1);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }

        .technical-details h4 {
            color: var(--primary-color);
            margin-top: 0;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 6px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .feature-list li:before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
            font-size: 14px;
        }

        .popup-state {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 600;
        }

        .popup-state.minimized {
            background-color: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 1px dashed #f39c12;
        }

        .popup-state.reopened {
            background-color: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes restored-glow {

            0%,
            100% {
                box-shadow: 0 0 0 rgba(46, 204, 113, 0);
            }

            50% {
                box-shadow: 0 0 20px rgba(46, 204, 113, 0.4);
            }
        }
    </style>
</head>

<body>
    <h1>🔄 Persistent Upload Status - FIXED!</h1>
    <p>The extension now maintains upload progress even when you minimize and reopen the popup during large file
        uploads.</p>

    <!-- Problem & Solution -->
    <div class="demo-section">
        <h2>
            🎯 Problem Solved
            <span class="fix-status">✓ Fixed</span>
        </h2>

        <p><strong>Issue:</strong> When minimizing the popup during large file uploads, the progress was lost when
            reopening.</p>
        <p><strong>Root Cause:</strong> Progress tracking was only stored in memory and lost when popup was destroyed.
        </p>
        <p><strong>Solution:</strong> Implemented persistent state storage and progress restoration system.</p>

        <div class="workflow-steps">
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">Start Large File Upload</div>
                    <div class="step-description">User clicks Send for a large file (>5MB)</div>
                </div>
                <div class="step-status after">Active</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">Save Upload State</div>
                    <div class="step-description">Upload state saved to Chrome storage with request ID, timing, and file
                        info</div>
                </div>
                <div class="step-status after">Persistent</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">User Minimizes Popup</div>
                    <div class="step-description">Popup closes but upload continues in background</div>
                </div>
                <div class="step-status after">No Loss</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">User Reopens Popup</div>
                    <div class="step-description">Popup loads and checks for saved upload state</div>
                </div>
                <div class="step-status after">Restored</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <div class="step-title">Resume Progress Tracking</div>
                    <div class="step-description">Progress polling resumes and UI shows current upload status</div>
                </div>
                <div class="step-status after">Seamless</div>
            </div>
        </div>
    </div>

    <!-- Demo Interface -->
    <div class="demo-section">
        <h2>📱 User Experience Demo</h2>

        <div class="demo-interface">
            <h4>Before Fix - Progress Lost:</h4>
            <div class="demo-popup">
                <div class="popup-state minimized">🔽 Popup Minimized - Progress Lost</div>
                <button class="demo-send-btn normal">Send</button>
                <small>❌ No way to recover upload status</small>
            </div>
        </div>

        <div class="demo-interface">
            <h4>After Fix - Progress Restored:</h4>
            <div class="demo-popup">
                <div class="popup-state reopened">🔄 Popup Reopened - Progress Restored!</div>
                <button class="demo-send-btn restored" id="demo-restored-btn">
                    <div class="demo-spinner"></div>
                    Sending... <span class="progress-percentage">67%</span>
                </button>
                <small>✅ Upload progress automatically restored and continues</small>
            </div>
        </div>
    </div>

    <!-- Technical Implementation -->
    <div class="demo-section">
        <h2>🛠️ Technical Implementation</h2>

        <div class="technical-details">
            <h4>State Persistence System:</h4>
            <ul class="feature-list">
                <li><strong>Chrome Storage:</strong> Upload state saved to chrome.storage.local</li>
                <li><strong>Request Tracking:</strong> Unique request ID for each upload</li>
                <li><strong>Timing Data:</strong> Start time and duration tracking</li>
                <li><strong>File Metadata:</strong> Size and type information preserved</li>
            </ul>
        </div>

        <div class="technical-details">
            <h4>Restoration Process:</h4>
            <ul class="feature-list">
                <li><strong>Popup Load:</strong> Checks for saved upload state on initialization</li>
                <li><strong>Progress Query:</strong> Contacts background script for current progress</li>
                <li><strong>UI Restoration:</strong> Button state and percentage display restored</li>
                <li><strong>Polling Resume:</strong> Progress tracking continues seamlessly</li>
            </ul>
        </div>

        <div class="technical-details">
            <h4>Cleanup & Error Handling:</h4>
            <ul class="feature-list">
                <li><strong>Completion Cleanup:</strong> State cleared when upload finishes</li>
                <li><strong>Error Recovery:</strong> Handles cases where upload completed during closure</li>
                <li><strong>Timeout Handling:</strong> Detects stale uploads and cleans up</li>
                <li><strong>Memory Management:</strong> Prevents storage bloat</li>
            </ul>
        </div>
    </div>

    <!-- Files Modified -->
    <div class="demo-section">
        <h2>📁 Files Modified</h2>

        <div class="technical-details">
            <h4>popup.js - Main Changes:</h4>
            <ul class="feature-list">
                <li><strong>State Structure:</strong> Added activeUpload tracking object</li>
                <li><strong>Save Functions:</strong> saveUploadState() and clearUploadState()</li>
                <li><strong>Restoration Logic:</strong> resumeProgressTracking() function</li>
                <li><strong>Initialization:</strong> Check for saved state on popup load</li>
            </ul>
        </div>

        <div class="technical-details">
            <h4>Key Features Added:</h4>
            <ul class="feature-list">
                <li><strong>Persistent Storage:</strong> Upload state survives popup closure</li>
                <li><strong>Automatic Detection:</strong> Recognizes ongoing uploads on startup</li>
                <li><strong>Progress Continuity:</strong> Seamless progress bar restoration</li>
                <li><strong>Error Resilience:</strong> Handles various edge cases gracefully</li>
            </ul>
        </div>
    </div>

    <!-- Usage Instructions -->
    <div class="demo-section">
        <h2>🧪 How to Test</h2>

        <div class="workflow-steps">
            <div class="workflow-step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">Upload Large File</div>
                    <div class="step-description">Select a file >10MB and click Send</div>
                </div>
                <div class="step-status after">Ready</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">Watch Progress Start</div>
                    <div class="step-description">See "Sending... X%" with progress percentage</div>
                </div>
                <div class="step-status after">Normal</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">Minimize Extension</div>
                    <div class="step-description">Close the popup while upload is in progress</div>
                </div>
                <div class="step-status after">Test</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">Reopen Extension</div>
                    <div class="step-description">Click extension icon to reopen popup</div>
                </div>
                <div class="step-status after">Magic</div>
            </div>

            <div class="workflow-step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <div class="step-title">Progress Restored!</div>
                    <div class="step-description">Upload status shows current progress percentage</div>
                </div>
                <div class="step-status after">Success</div>
            </div>
        </div>

        <div
            style="padding: 15px; background-color: rgba(46, 204, 113, 0.1); border: 2px solid var(--success-color); border-radius: 8px; text-align: center; margin-top: 20px;">
            <h3 style="color: var(--success-color); margin-top: 0;">🎉 No More Lost Progress!</h3>
            <p style="margin-bottom: 0;">You can now safely minimize the extension during large uploads and come back to
                see the exact progress status.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔄 Persistent Upload Status Demo loaded!');

            // Simulate progress updates for demo
            const progressBtn = document.getElementById('demo-restored-btn');
            if (progressBtn) {
                let progress = 67;
                const progressSpan = progressBtn.querySelector('.progress-percentage');

                const updateProgress = () => {
                    progress += Math.random() * 8 + 2;
                    if (progress >= 100) {
                        progress = 100;
                        progressSpan.textContent = '100%';
                        progressBtn.innerHTML = '✅ Completed!';
                        progressBtn.classList.remove('restored');
                        progressBtn.classList.add('normal');
                        clearInterval(progressInterval);

                        setTimeout(() => {
                            progressBtn.innerHTML = 'Send';
                            progressBtn.style.backgroundColor = 'var(--primary-color)';
                        }, 3000);
                    } else {
                        progressSpan.textContent = Math.round(progress) + '%';
                    }
                };

                const progressInterval = setInterval(updateProgress, 1000);
            }
        });
    </script>
</body>

</html>