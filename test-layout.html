<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi Webhook Sender - Layout Test</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Test-specific styles */
        .test-container {
            width: 700px;
            height: 550px;
            margin: 20px auto;
            border: 2px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .test-info {
            margin: 20px auto;
            max-width: 700px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 6px;
            font-family: Arial, sans-serif;
        }

        .test-info h2 {
            color: #333;
            margin-top: 0;
        }

        .test-info ul {
            padding-left: 20px;
        }

        .test-info li {
            margin-bottom: 5px;
        }

        .keyboard-shortcuts {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .keyboard-shortcuts strong {
            color: #0066cc;
        }
    </style>
</head>

<body>
    <div class="test-info">
        <h2>Multi Webhook Sender - Layout Enhancements Test</h2>
        <p><strong>Successfully implemented features:</strong></p>
        <ul>
            <li>✅ <strong>Removed Collapsible Sections</strong> - Key-value fields are always accessible as requested
            </li>
            <li>✅ <strong>Resizable Panels</strong> - Drag the separator between panels to adjust width</li>
            <li>✅ <strong>Full-Screen Mode</strong> - Click the expand button (top-right) to hide left panel</li>
            <li>❌ <strong>Compact Mode Removed</strong> - Was making buttons too small and reducing usability</li>
        </ul>

        <div class="keyboard-shortcuts">
            <strong>Keyboard Shortcuts:</strong><br>
            • <strong>Ctrl/Cmd + Shift + F:</strong> Toggle Full-Screen Mode<br>
            • <strong>Double-click resize handle:</strong> Reset panel width to default
        </div>

        <p><strong>Interaction Instructions:</strong></p>
        <ul>
            <li><strong>Resize Panels:</strong> Drag the gray separator between left and right panels</li>
            <li><strong>Full-Screen:</strong> Look for the expand icon in the editor header (top-right)</li>
            <li><strong>Panel Width:</strong> Constraints: 180px minimum, 400px maximum</li>
            <li><strong>Button Layout:</strong> Now properly sized and visible without cramping</li>
        </ul>
    </div>

    <div class="test-container">
        <!-- Copy the exact structure from popup.html -->
        <div class="container">
            <div class="panel-left" id="panel-left">
                <div class="list-header">
                    <h3>Saved Requests</h3>
                    <div class="header-actions">
                        <button id="sort-btn" class="header-btn sort-btn">
                            <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z" />
                            </svg>
                        </button>
                        <button id="theme-toggle-btn" class="header-btn theme-btn" title="Switch to Dark Mode">
                            <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                <path
                                    d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z" />
                            </svg>
                        </button>
                        <button id="add-new-request-btn" class="header-btn add-btn">
                            <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="search-wrapper">
                    <input type="text" id="search-input" placeholder="Search requests...">
                </div>
                <ul id="requests-list" class="requests-list">
                    <li class="request-item selected">
                        <div class="request-content">
                            <div class="request-name">Test Request 1</div>
                            <div class="request-meta">POST • Created 2 hours ago</div>
                        </div>
                    </li>
                    <li class="request-item">
                        <div class="request-content">
                            <div class="request-name">API Call Example</div>
                            <div class="request-meta">GET • Created yesterday</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="resize-handle" id="resize-handle"></div>
            <div class="panel-right" id="panel-right">
                <div id="request-editor" class="request-editor" tabindex="-1">
                    <div class="editor-header">
                        <div class="request-name-wrapper">
                            <input type="text" id="request-name" placeholder="Request Name" class="request-name-input"
                                value="Test Request 1">
                        </div>
                        <div class="editor-header-actions">
                            <button id="fullscreen-btn" class="header-btn fullscreen-btn"
                                title="Toggle Full-Screen Mode">
                                <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                    <path
                                        d="M5,5H10V7H7V10H5V5M14,5H19V10H17V7H14V5M17,14H19V19H14V17H17V14M10,17V19H5V14H7V17H10Z" />
                                </svg>
                            </button>
                            <div class="body-type-switcher">
                                <label for="body-type-select">Body Type</label>
                                <select id="body-type-select">
                                    <option value="json">JSON (Key-Value)</option>
                                    <option value="raw">Raw Text</option>
                                    <option value="form-data">Form-Data (Files)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="editor-content">
                        <div class="request-form">
                            <div class="form-group url-group">
                                <label for="url">URL</label>
                                <div class="url-input-wrapper">
                                    <input type="text" id="url" placeholder="https://api.example.com/data"
                                        value="https://webhook.site/test">
                                    <button id="test-url-btn" class="test-btn">Test</button>
                                </div>
                            </div>

                            <div class="headers-section">
                                <div class="headers-header">
                                    <h4>Headers</h4>
                                    <button id="add-header-btn" class="add-btn-small">
                                        <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                        </svg>
                                    </button>
                                </div>
                                <div id="headers-container">
                                    <div class="header-item">
                                        <div class="header-inputs">
                                            <input type="text" class="header-key" placeholder="Key"
                                                value="Content-Type">
                                            <input type="text" class="header-value" placeholder="Value"
                                                value="application/json">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="simple-body-editor" class="body-section">
                                <div class="body-header">
                                    <h4>Body (Key-Value)</h4>
                                    <button id="add-simple-row-btn" class="add-btn-small">
                                        <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                        </svg>
                                    </button>
                                </div>
                                <div id="simple-body-container">
                                    <div class="simple-body-item">
                                        <input type="text" placeholder="Key" value="message">
                                        <input type="text" placeholder="Value" value="Hello World">
                                    </div>
                                    <div class="simple-body-item">
                                        <input type="text" placeholder="Key" value="timestamp">
                                        <input type="text" placeholder="Value" value="2024-01-15T10:30:00Z">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simplified version of the main functionality for testing
        document.addEventListener('DOMContentLoaded', () => {
            const resizeHandle = document.getElementById('resize-handle');
            const panelLeft = document.getElementById('panel-left');
            const container = document.querySelector('.container');
            const fullScreenBtn = document.getElementById('fullscreen-btn');
            const body = document.body;

            // Resizable panels
            let isResizing = false;
            let startX = 0;
            let startLeftWidth = 0;

            if (resizeHandle && panelLeft && container) {
                resizeHandle.addEventListener('mousedown', (e) => {
                    isResizing = true;
                    startX = e.clientX;
                    startLeftWidth = parseInt(window.getComputedStyle(panelLeft).width, 10);
                    document.body.style.cursor = 'col-resize';
                    e.preventDefault();
                });

                document.addEventListener('mousemove', (e) => {
                    if (!isResizing) return;
                    const deltaX = e.clientX - startX;
                    const newLeftWidth = startLeftWidth + deltaX;
                    const clampedWidth = Math.max(180, Math.min(newLeftWidth, 400));
                    panelLeft.style.width = `${clampedWidth}px`;
                });

                document.addEventListener('mouseup', () => {
                    if (isResizing) {
                        isResizing = false;
                        document.body.style.cursor = '';
                    }
                });

                resizeHandle.addEventListener('dblclick', () => {
                    panelLeft.style.width = '220px';
                });
            }

            // Full-screen mode
            if (fullScreenBtn && container) {
                fullScreenBtn.addEventListener('click', () => {
                    const isFullScreen = container.classList.contains('fullscreen-mode');
                    if (isFullScreen) {
                        container.classList.remove('fullscreen-mode');
                    } else {
                        container.classList.add('fullscreen-mode');
                    }
                });
            }
        });
    </script>
</body>

</html>