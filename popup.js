// Unidirectional Data Flow Implementation
document.addEventListener('DOMContentLoaded', () => {
    // --- DOM Elements ---
    const requestsListEl = document.getElementById('requests-list');
    const addNewRequestBtn = document.getElementById('add-new-request-btn');
    const sortBtn = document.getElementById('sort-btn');
    const sortMenuEl = document.getElementById('sort-menu');
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const searchInputEl = document.getElementById('search-input');
    const requestNameInput = document.getElementById('request-name');
    const saveNameBtn = document.getElementById('save-name-btn');
    const bodyTypeSelect = document.getElementById('body-type-select');
    const advancedOptionsEl = document.getElementById('advanced-options');
    const httpMethodSelect = document.getElementById('http-method');
    const urlInput = document.getElementById('url');
    const headersContainerEl = document.getElementById('headers-container');
    const addHeaderBtn = document.getElementById('add-header-btn');
    const bodyInput = document.getElementById('body-input'); // Raw body
    const simpleBodyEditorEl = document.getElementById('simple-body-editor');
    const advancedBodyEditorEl = document.getElementById('advanced-body-editor');
    const simpleBodyContainerEl = document.getElementById('simple-body-container');
    const formDataEditorEl = document.getElementById('form-data-editor');
    const formDataContainerEl = document.getElementById('form-data-container');
    const addFormDataRowBtn = document.getElementById('add-form-data-row-btn');
    const valueCharWordCountEl = document.getElementById('value-char-word-count');
    const charCountEl = document.getElementById('char-count');
    const wordCountEl = document.getElementById('word-count');
    const addSimpleRowBtn = document.getElementById('add-simple-row-btn');
    const testUrlBtn = document.getElementById('test-url-btn');
    const sendBtn = document.getElementById('send-btn');
    const deleteRequestBtn = document.getElementById('delete-request-btn');
    const exportBtn = document.getElementById('export-btn');
    const importFileEl = document.getElementById('import-file');
    const clearRawBodyBtn = document.getElementById('clear-raw-body-btn');
    const notificationBarEl = document.getElementById('notification-bar');
    const responseViewerEl = document.getElementById('response-viewer');
    const responseStatusEl = document.getElementById('response-status');
    const responseBodyEl = document.getElementById('response-body');
    const extremeCleanCheckbox = document.getElementById('extreme-clean-checkbox');
    const storageCleanupBtn = document.getElementById('storage-cleanup-btn');

    // --- State Management ---
    let state = {
        requests: [],
        selectedRequestId: null,
        sortBy: 'useCount', // Default sort
        searchQuery: '',
        settings: {
            extremeClean: false,
            keepData: false, // Add keepData to the default state
        },
        // Upload status tracking
        activeUpload: {
            requestId: null,
            isUploading: false,
            startTime: null,
            hasLargeFiles: false
        }
    };
    let deleteFieldConfirmState = { type: null, index: null };
    let deleteFieldTimeout = null;
    let originalRequestName = '';

    // --- Core Functions ---

    /**
     * Gets an appropriate icon for the file type
     * @function getFileTypeIcon
     * @param {string} extension - File extension including the dot
     * @returns {string} HTML for the file type icon
     */
    function getFileTypeIcon(extension) {
        const ext = extension.toLowerCase();

        // Video files
        if (['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'].includes(ext)) {
            return '📹'; // Video camera emoji
        }

        // Image files
        if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico', '.tiff'].includes(ext)) {
            return '🖼️'; // Picture frame emoji
        }

        // Audio files
        if (['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a'].includes(ext)) {
            return '🎧'; // Headphones emoji
        }

        // Document files
        if (['.pdf', '.doc', '.docx', '.txt', '.rtf'].includes(ext)) {
            return '📄'; // Document emoji
        }

        // Spreadsheet files
        if (['.xls', '.xlsx', '.csv'].includes(ext)) {
            return '📈'; // Chart emoji
        }

        // Archive files
        if (['.zip', '.rar', '.7z', '.tar', '.gz'].includes(ext)) {
            return '🗇'; // Archive emoji
        }

        // Code files
        if (['.js', '.html', '.css', '.json', '.xml', '.py', '.java', '.cpp', '.c'].includes(ext)) {
            return '💻'; // Computer emoji
        }

        // Default file icon
        return '📁'; // File folder emoji
    }

    /**
     * Checks if the file is a video file
     * @function isVideoFile
     * @param {string} extension - File extension including the dot
     * @returns {boolean} True if video file
     */
    function isVideoFile(extension) {
        const videoExtensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ogv'];
        return videoExtensions.includes(extension.toLowerCase());
    }

    /**
     * Checks if the file is an image file
     * @function isImageFile
     * @param {string} extension - File extension including the dot
     * @returns {boolean} True if image file
     */
    function isImageFile(extension) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico', '.tiff'];
        return imageExtensions.includes(extension.toLowerCase());
    }

    /**
     * Formats file size in human-readable format
     * @function formatFileSize
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * Initializes the theme based on stored preferences
     * @async
     * @function initializeTheme
     * @returns {Promise<void>}
     */
    async function initializeTheme() {
        try {
            const { theme } = await chrome.storage.local.get({ theme: 'light' });
            document.documentElement.setAttribute('data-theme', theme);
            updateThemeIcon(theme);
        } catch (error) {
            console.error('Failed to initialize theme:', error);
            // Fallback to light theme
            document.documentElement.setAttribute('data-theme', 'light');
            updateThemeIcon('light');
        }
    }

    /**
     * Updates the theme toggle button icon based on current theme
     * @function updateThemeIcon
     * @param {string} theme - Current theme ('light' or 'dark')
     */
    function updateThemeIcon(theme) {
        const themeIcon = themeToggleBtn.querySelector('svg path');
        if (theme === 'dark') {
            // Sun icon for switching to light mode
            themeIcon.setAttribute('d', 'M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z');
            themeToggleBtn.title = 'Switch to Light Mode';
        } else {
            // Moon icon for switching to dark mode
            themeIcon.setAttribute('d', 'M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z');
            themeToggleBtn.title = 'Switch to Dark Mode';
        }
    }

    /**
     * Toggles between light and dark theme
     * @async
     * @function toggleTheme
     * @returns {Promise<void>}
     */
    async function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';

        document.documentElement.setAttribute('data-theme', newTheme);
        updateThemeIcon(newTheme);

        await chrome.storage.local.set({ theme: newTheme });
        showNotification(`Switched to ${newTheme} theme`, 'success', 1500);
    }

    /**
     * Loads initial state from Chrome storage
     * @async
     * @function loadInitialState
     * @returns {Promise<void>}
     */
    async function loadInitialState() {
        try {
            // Clean up orphaned temp files on startup
            await cleanupOrphanedTempFiles(15); // Clean files older than 15 minutes

            const { requests, settings, sortBy, uploadState } = await chrome.storage.local.get({
                requests: [],
                settings: { extremeClean: false, keepData: false },
                sortBy: 'useCount',
                uploadState: null

            state.requests = requests || [];
                state.settings = { ...state.settings, ...settings };
                state.sortBy = sortBy || 'useCount';

                // Restore upload state if exists
                if(uploadState && uploadState.isUploading) {
                    state.activeUpload = uploadState;
            console.log('Restored active upload state:', uploadState);
            // Check if upload is still active and resume progress tracking
            await resumeProgressTracking();
        }

            // Update UI
            extremeCleanCheckbox.checked = state.settings.extremeClean;
        render();
        if (state.requests.length > 0) {
            selectRequest(state.requests[0].id);
        }
    } catch (error) {
        console.error('Failed to load initial state:', error);
    }
}

    /**
     * Saves upload state to Chrome storage for persistence
     * @async
     * @function saveUploadState
     * @returns {Promise<void>}
     */
    async function saveUploadState() {
        try {
            await chrome.storage.local.set({
                uploadState: state.activeUpload
            } catch (error) {
                console.error('Failed to save upload state:', error);
            }
        }

    /**
     * Clears upload state from Chrome storage
     * @async
     * @function clearUploadState
     * @returns {Promise<void>}
     */
    async function clearUploadState() {
            try {
                await chrome.storage.local.remove('uploadState');
                state.activeUpload = {
                    requestId: null,
                    isUploading: false,
                    startTime: null,
                    hasLargeFiles: false
                };
            } catch (error) {
                console.error('Failed to clear upload state:', error);
            }
        }

        /**
         * Resumes progress tracking for ongoing upload
         * @async
         * @function resumeProgressTracking
         * @returns {Promise<void>}
         */
        async function resumeProgressTracking() {
            if (!state.activeUpload.isUploading || !state.activeUpload.requestId) {
                return;
            }

            console.log('Resuming progress tracking for request:', state.activeUpload.requestId);

            // Update UI to show uploading state
            sendBtn.classList.add('sending');
            sendBtn.disabled = true;
            const btnText = sendBtn.querySelector('.btn-text');
            btnText.innerHTML = 'Sending... <span class="progress-percentage">Checking...</span>';

            // Start polling for progress
            const progressInterval = setInterval(() => {
                chrome.runtime.sendMessage({
                    action: 'getProgress',
                    requestId: state.activeUpload.requestId
                }, (progressResponse) => {
                    if (progressResponse && progressResponse.progress) {
                        const progress = progressResponse.progress;
                        const progressSpan = btnText.querySelector('.progress-percentage');

                        if (progressSpan && progress.progress !== undefined) {
                            progressSpan.textContent = `${progress.progress}%`;

                            // Update button style based on progress stage
                            if (progress.stage === 'complete') {
                                clearInterval(progressInterval);
                                progressSpan.textContent = '100%';
                                handleUploadComplete(true);
                            } else if (progress.stage === 'error') {
                                clearInterval(progressInterval);
                                progressSpan.textContent = 'Error';
                                handleUploadComplete(false, progress.error);
                            }
                        }
                    } else {
                        // No progress found, upload might have completed while popup was closed
                        console.log('No progress found, checking if upload completed');
                        clearInterval(progressInterval);
                        handleUploadComplete(true, 'Upload may have completed while popup was closed');
                    }
                }, 500);

                // Store interval reference for cleanup
                state.activeUpload.progressInterval = progressInterval;
            }

    /**
     * Handles upload completion and cleanup
     * @function handleUploadComplete
     * @param {boolean} success - Whether upload was successful
     * @param {string} [message] - Optional completion message
     */
    async function handleUploadComplete(success, message) {
                    // Clean up UI
                    sendBtn.classList.remove('loading', 'sending');
                    sendBtn.disabled = false;
                    const btnText = sendBtn.querySelector('.btn-text');

                    if (success) {
                        btnText.textContent = '✔';
                        sendBtn.style.backgroundColor = 'var(--success-color)';
                        showNotification(message || '🚀 Upload completed successfully!', 'success');
                    } else {
                        btnText.textContent = '✖';
                        sendBtn.style.backgroundColor = 'var(--error-color)';
                        showNotification(message || 'Upload failed', 'error');
                    }

                    // Reset button after delay
                    setTimeout(() => {
                        btnText.textContent = 'Send';
                        btnText.innerHTML = 'Send';
                        sendBtn.style.backgroundColor = 'var(--primary-color)';
                    }, 2000);

                    // Clear upload state
                    await clearUploadState();
                }
    async function loadInitialState() {
                    try {
                        const data = await chrome.storage.local.get({ requests: [], settings: { extremeClean: false, keepData: false } });
                        const sessionData = await chrome.storage.session.get({ lastSelectedId: null, sortBy: 'useCount' });

                        state.requests = data.requests;
                        state.settings = data.settings;
                        state.sortBy = sessionData.sortBy;

                        // If no requests exist, create a default one
                        if (state.requests.length === 0) {
                            const now = Date.now();
                            const defaultRequest = {
                                id: `req_${now}`,
                                name: 'My First Request',
                                isAdvanced: false,
                                method: 'POST',
                                url: '',
                                headers: [{ key: 'Content-Type', value: 'application/json' }],
                                simpleBody: [{ key: '', value: '' }],
                                body: '',
                                createdAt: now,
                                useCount: 0,
                                lastUsed: 0,
                                isPinned: false, // New property for pinning
                            };
                            state.requests.push(defaultRequest);
                            saveState(); // Save the new default request
                        }

                        const idToSelect = sessionData.lastSelectedId || (state.requests.length > 0 ? state.requests[0].id : null);

                        if (idToSelect) {
                            selectRequest(idToSelect);
                        } else {
                            render();
                        }
                    } catch (error) {
                        console.error('Failed to load initial state:', error);
                        showNotification('Failed to load saved data. Starting fresh.', 'error', 3000);

                        // Initialize with default state
                        state.requests = [];
                        state.settings = { extremeClean: false, keepData: false };
                        state.sortBy = 'useCount';
                        render();
                    }
                }

    /**
     * Toggles pin status for a request
     * @function togglePin
     * @param {string} id - Request ID to toggle pin status
     */
    function togglePin(id) {
                    const request = state.requests.find(r => r.id === id);
                    if (request) {
                        request.isPinned = !request.isPinned;
                        saveState();
                        render();
                    }
                }

    /**
     * Creates a duplicate of an existing request
     * @function duplicateRequest
     * @param {string} id - Request ID to duplicate
     */
    function duplicateRequest(id) {
                    const originalRequest = state.requests.find(r => r.id === id);
                    if (originalRequest) {
                        const now = Date.now();
                        const duplicatedRequest = {
                            ...originalRequest,
                            id: `req_${now}`,
                            name: `${originalRequest.name} (Copy)`,
                            createdAt: now,
                            useCount: 0,
                            lastUsed: 0,
                            isPinned: false
                        };
                        state.requests.push(duplicatedRequest);
                        saveState();
                        selectRequest(duplicatedRequest.id);
                        showNotification('Request duplicated successfully', 'success', 2000);
                    }
                }

    /**
     * Selects a request for viewing/editing
     * @function selectRequest
     * @param {string} id - Request ID to select
     */
    function selectRequest(id) {
                    if (state.selectedRequestId !== id) {
                        state.selectedRequestId = id;
                        chrome.storage.session.set({ lastSelectedId: id });
                        const selectedRequest = state.requests.find(r => r.id === id);
                        if (selectedRequest) {
                            // This is a remnant of the old toggle switch and is no longer needed.
                            // The render() function now handles setting the body type dropdown.
                        }
                        render();
                    }
                }

    /**
     * Renders the entire UI based on current state
     * @function render
     * @returns {void}
     */
    function render() {
                    const selectedRequest = state.requests.find(r => r.id === state.selectedRequestId);
                    // --- Filter and Sort Requests ---
                    const filteredRequests = state.requests.filter(req =>
                        req.name.toLowerCase().includes(state.searchQuery.toLowerCase())
                    );

                    const sortedRequests = [...filteredRequests].sort((a, b) => {
                        // Always put pinned items first
                        if (a.isPinned && !b.isPinned) return -1;
                        if (!a.isPinned && b.isPinned) return 1;

                        // Then apply the selected sort within each group
                        switch (state.sortBy) {
                            case 'createdAt':
                                return (b.createdAt || 0) - (a.createdAt || 0);
                            case 'useCount':
                                // Sort by use count descending, then by lastUsed descending as secondary sort
                                const useCountDiff = (b.useCount || 0) - (a.useCount || 0);
                                if (useCountDiff !== 0) return useCountDiff;
                                return (b.lastUsed || 0) - (a.lastUsed || 0);
                            case 'name':
                            default:
                                return a.name.localeCompare(b.name);
                        }

                        // --- Start: Focus Management ---
                        let activeElement = document.activeElement;
                        let activeElementState = null;
                        if (activeElement) {
                            if (activeElement.id === 'search-input') {
                                activeElementState = {
                                    identifier: 'search-input',
                                    selectionStart: activeElement.selectionStart,
                                    selectionEnd: activeElement.selectionEnd
                                };
                            } else if (activeElement.parentElement.matches('.header-item, .simple-body-item')) {
                                const parent = activeElement.parentElement;
                                const removeBtn = parent.querySelector('.remove-header-btn, .remove-simple-row-btn');
                                if (removeBtn && removeBtn.dataset) {
                                    const index = removeBtn.dataset.index;
                                    const type = parent.className.split('-')[0]; // 'header' or 'simple'
                                    const fieldClass = activeElement.className;
                                    let field = 'key';
                                    if (fieldClass.includes('value')) field = 'value';
                                    activeElementState = {
                                        identifier: `${type}-${index}-${field}`,
                                        selectionStart: activeElement.selectionStart,
                                        selectionEnd: activeElement.selectionEnd
                                    };
                                }
                            }
                        }
                        // --- End: Focus Management ---

                        // Render request list
                        requestsListEl.innerHTML = '';
                        if (sortedRequests.length === 0) {
                            const li = document.createElement('li');
                            li.textContent = 'No webhooks yet. Add one!';
                            li.className = 'empty-state';
                            requestsListEl.appendChild(li);
                        } else {
                            sortedRequests.forEach(req => {
                                const li = document.createElement('li');
                                li.className = `request-item ${req.id === state.selectedRequestId ? 'selected' : ''}`;
                                li.dataset.id = req.id;
                                li.onclick = () => selectRequest(req.id);

                                const text = document.createElement('span');
                                text.textContent = req.name;
                                text.className = 'request-item-name';

                                const pin = document.createElement('button'); // Changed to button for semantics
                                pin.innerHTML = req.isPinned ? '&#9733;' : '&#9734;'; // Star icons
                                pin.className = `icon-btn request-item-pin ${req.isPinned ? 'pinned' : ''}`;
                                pin.title = req.isPinned ? 'Unpin from right-click context menu' : 'Pin to right-click context menu';
                                pin.onclick = (e) => {
                                    e.stopPropagation();
                                    togglePin(req.id);
                                };

                                li.appendChild(text);
                                li.appendChild(pin);
                                requestsListEl.appendChild(li);
                            }

        if (!selectedRequest) {
                                // TODO: Show an empty state
                                return;
                            }

                            // Render editor panel
                            requestNameInput.value = selectedRequest.name;
                            urlInput.value = selectedRequest.url;
                            httpMethodSelect.value = selectedRequest.method;
                            bodyTypeSelect.value = selectedRequest.bodyType || 'json';

                            // Render headers
                            headersContainerEl.innerHTML = '';
                            selectedRequest.headers.forEach((header, index) => {
                                const div = document.createElement('div');
                                div.className = 'header-item';
                                // Add a wrapper for the inputs to control flex layout
                                div.innerHTML = `
                <div class="header-inputs">
                    <input type="text" class="header-key" placeholder="Key" value="${header.key}">
                    <div class="value-input-wrapper ${header.value ? 'has-content' : ''}">
                        <input type="text" class="header-value" placeholder="Value" value="${header.value}">
                        <button class="icon-btn clear-header-value-btn" data-index="${index}" title="Clear value">
                            <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                            </svg>
                        </button>
                    </div>
                </div>
                <button class="icon-btn remove-header-btn" data-index="${index}">-</button>
            `;
                                headersContainerEl.appendChild(div);

                                // Render body editors
                                simpleBodyContainerEl.innerHTML = '';
                                selectedRequest.simpleBody.forEach((item, index) => {
                                    const div = document.createElement('div');
                                    div.className = 'simple-body-item';
                                    div.innerHTML = `
                <textarea class="simple-body-key" placeholder="Key" rows="1">${item.key}</textarea>
                <div class="value-input-wrapper ${item.value ? 'has-content' : ''}">
                    <textarea class="simple-body-value" placeholder="Value" rows="1">${item.value}</textarea>
                    <button class="icon-btn clear-value-btn" data-index="${index}" title="Clear value">
                        <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                        </svg>
                    </button>
                </div>
                <button class="icon-btn remove-simple-row-btn" data-index="${index}">-</button>
            `;
                                    simpleBodyContainerEl.appendChild(div);

                                    formDataContainerEl.innerHTML = '';
                                    if (selectedRequest.formDataBody) {
                                        selectedRequest.formDataBody.forEach((item, index) => {
                                            const div = document.createElement('div');
                                            div.className = 'form-data-item';
                                            let valueContent;
                                            if (item.type === 'text') {
                                                // Use a textarea for better multi-line editing
                                                valueContent = `
                        <div class="value-input-wrapper ${item.value ? 'has-content' : ''}">
                            <textarea class="form-data-value" placeholder="Value" rows="1">${item.value || ''}</textarea>
                            <button class="icon-btn clear-value-btn" data-index="${index}" title="Clear value">
                                <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                    <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                </svg>
                            </button>
                        </div>
                    `;
                                            } else { // type is 'file'
                                                if (item.fileName) {
                                                    // Enhanced file display with icons and file info
                                                    const safeName = item.fileName;
                                                    const dotIndex = safeName.lastIndexOf('.');
                                                    const base = dotIndex > 0 ? safeName.slice(0, dotIndex) : safeName;
                                                    const ext = dotIndex > 0 ? safeName.slice(dotIndex) : '';
                                                    const fileIcon = getFileTypeIcon(ext);
                                                    const fileSize = item.fileSize ? formatFileSize(item.fileSize) : '';
                                                    const isVideo = isVideoFile(ext);
                                                    const isImage = isImageFile(ext);
                                                    const isPlaceholder = item.isPlaceholder || (item.value && item.value.startsWith('[LARGE_FILE_PLACEHOLDER]'));
                                                    const isFreshUpload = item.isFreshUpload || false;
                                                    const isLargeFile = item.fileSize && item.fileSize > 10 * 1024 * 1024;

                                                    valueContent = `
                            <div class="form-data-file-display">
                                <div class="file-info">
                                    <div class="file-icon ${isVideo ? 'video-file' : ''} ${isImage ? 'image-file' : ''} ${isPlaceholder ? 'placeholder-file' : ''} ${isFreshUpload ? 'fresh-upload' : ''}">
                                        ${isPlaceholder ? '⚠️' : isFreshUpload && isLargeFile ? '🚀' : fileIcon}
                                    </div>
                                    <div class="file-details">
                                        <div class="form-data-filename" title="${safeName}">
                                            <span class="filename-start">${base}</span><span class="filename-ext">${ext}</span>
                                        </div>
                                        ${fileSize ? `<div class="file-size">${fileSize}</div>` : ''}
                                        ${isVideo && !isPlaceholder && !isFreshUpload ? '<div class="file-type-label">📹 Video File</div>' : ''}
                                        ${isImage && !isPlaceholder && !isFreshUpload ? '<div class="file-type-label">🖼️ Image File</div>' : ''}
                                        ${isFreshUpload && isLargeFile ? '<div class="file-type-label success">🚀 Large File Ready</div>' : ''}
                                        ${isVideo && isFreshUpload && isLargeFile ? '<div class="file-type-label success">📹 Video Ready to Send</div>' : ''}
                                        ${isPlaceholder ? '<div class="file-type-label warning">⚠️ Large File (Re-upload needed)</div>' : ''}
                                    </div>
                                </div>
                                <button class="icon-btn clear-file-btn" data-index="${index}" title="Clear file">✕</button>
                            </div>
                        `;
                                                } else {
                                                    valueContent = `
                            <div class="file-upload-area" data-index="${index}">
                                <div class="upload-content">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">
                                        <span class="upload-main">Choose file or drag & drop</span>
                                        <span class="upload-hint">Supports videos, images, documents, and more</span>
                                    </div>
                                </div>
                                <input type="file" class="form-data-file" data-index="${index}" accept="*/*">
                                <div class="upload-progress hidden">
                                    <div class="progress-bar">
                                        <div class="progress-fill"></div>
                                    </div>
                                    <span class="progress-text">Uploading...</span>
                                </div>
                            </div>
                        `;
                                                }
                                            }

                                            div.innerHTML = `
                    <div class="form-data-controls">
                        <select class="form-data-type" data-index="${index}">
                            <option value="text" ${item.type === 'text' ? 'selected' : ''}>Text</option>
                            <option value="file" ${item.type === 'file' ? 'selected' : ''}>File</option>
                        </select>
                        <textarea class="form-data-key" placeholder="Key" rows="1">${item.key || ''}</textarea>
                    </div>
                    <div class="form-data-value-wrapper">
                        ${valueContent}
                    </div>
                    <button class="icon-btn remove-form-data-row-btn" data-index="${index}">-</button>
                `;
                                            formDataContainerEl.appendChild(div);
                                        }
        bodyInput.value = selectedRequest.body;

                                        // Highlight active sort option
                                        sortMenuEl.querySelectorAll('.sort-option').forEach(opt => {
                                            opt.classList.toggle('active', opt.dataset.value === state.sortBy);

                                            // This logic has been moved to the oninput handlers to prevent re-expansion on render.

                                            // --- Start: Restore Focus ---
                                            if (activeElementState) {
                                                if (activeElementState.identifier === 'search-input') {
                                                    setTimeout(() => {
                                                        const searchInput = document.getElementById('search-input');
                                                        if (searchInput) {
                                                            searchInput.focus();
                                                            searchInput.setSelectionRange(activeElementState.selectionStart, activeElementState.selectionEnd);
                                                        }
                                                    }, 0);
                                                } else {
                                                    const [type, index, field] = activeElementState.identifier.split('-');
                                                    const container = type === 'header' ? headersContainerEl : simpleBodyContainerEl;
                                                    const row = container.children[index];
                                                    if (row) {
                                                        const classToFind = type === 'header' ? `.header-${field}` : `.simple-body-${field}`;
                                                        const elementToFocus = row.querySelector(classToFind);
                                                        if (elementToFocus) {
                                                            elementToFocus.focus();
                                                            // Restore the exact cursor position
                                                            if (typeof elementToFocus.setSelectionRange === 'function') {
                                                                elementToFocus.setSelectionRange(activeElementState.selectionStart, activeElementState.selectionEnd);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            // --- End: Restore Focus ---
                                            else {
                                                // If no specific element was focused, use a timeout to run this *after*
                                                // the browser's default focus behavior has finished. This is a more
                                                // aggressive way to prevent the focus from jumping to the delete button.
                                                setTimeout(() => {
                                                    const editor = document.getElementById('request-editor');
                                                    if (editor) {
                                                        editor.focus();
                                                    }
                                                    // As a final failsafe, explicitly blur the delete button.
                                                    if (deleteRequestBtn) {
                                                        deleteRequestBtn.blur();
                                                    }
                                                }, 0);
                                            }

                                            // --- Final UI State Rendering ---
                                            const bodyType = selectedRequest.bodyType || 'json';

                                            // Render settings
                                            extremeCleanCheckbox.checked = selectedRequest.extremeClean || false;
                                            document.getElementById('keep-data-checkbox').checked = selectedRequest.keepData || false;

                                            // Show/hide body editors
                                            simpleBodyEditorEl.classList.toggle('hidden', bodyType !== 'json');
                                            advancedBodyEditorEl.classList.toggle('hidden', bodyType !== 'raw');
                                            formDataEditorEl.classList.toggle('hidden', bodyType !== 'form-data');

                                            // Hide "Clean JSON" option for Form-Data mode
                                            const cleanJsonWrapper = extremeCleanCheckbox.parentElement;
                                            if (bodyType === 'form-data') {
                                                cleanJsonWrapper.style.display = 'none';
                                            } else {
                                                cleanJsonWrapper.style.display = '';
                                            }

                                            // Explicitly show/hide advanced options. ONLY show for 'raw'.
                                            if (bodyType === 'raw') {
                                                advancedOptionsEl.classList.remove('hidden');
                                            } else {
                                                advancedOptionsEl.classList.add('hidden');
                                            }
                                        }

    function flattenObject(obj, parent = '', res = {}) {
                                                for (let key in obj) {
                                                    let propName = parent ? parent + '.' + key : key;
                                                    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                                                        flattenObject(obj[key], propName, res);
                                                    } else {
                                                        res[propName] = obj[key];
                                                    }
                                                }
                                                return res;
                                            }

    // Update the state in memory and schedule a save
    function updateState(field, value, index = null, shouldRender = true) {
                                                const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                if (!request) return;

                                                const isSwitchingBodyType = field === 'bodyType';

                                                if (field.includes('.')) {
                                                    const [parent, child] = field.split('.');
                                                    request[parent][index][child] = value;
                                                } else {
                                                    request[field] = value;
                                                }

                                                // When switching body types, intelligently translate the body AND update headers
                                                if (isSwitchingBodyType) {
                                                    const contentTypeHeaderIndex = request.headers.findIndex(h => h.key.toLowerCase() === 'content-type');

                                                    if (value === 'form-data') {
                                                        // If we find a Content-Type header, remove it, as the browser must set it.
                                                        if (contentTypeHeaderIndex > -1) {
                                                            request.headers.splice(contentTypeHeaderIndex, 1);
                                                        }
                                                    } else { // For 'json' and 'raw'
                                                        if (contentTypeHeaderIndex > -1) {
                                                            request.headers[contentTypeHeaderIndex].value = 'application/json';
                                                        } else {
                                                            // If no content-type header exists, add it.
                                                            request.headers.push({ key: 'Content-Type', value: 'application/json' });
                                                        }
                                                    }

                                                    // --- Handle Body Translation ---
                                                    if (value === 'form-data' && (!request.formDataBody || request.formDataBody.length === 0)) {
                                                        // If switching to an empty form-data, create a default file row
                                                        request.formDataBody = [{ type: 'file', key: 'file', value: '' }];
                                                    } else if (value === 'raw') { // Switching to Raw Text
                                                        // Check if there's existing JSON data to convert
                                                        const hasJsonData = request.simpleBody.some(item => item.key.trim() || item.value.trim());
                                                        if (hasJsonData) {
                                                            // Stringify the simple body to create the raw body
                                                            const simpleBodyObject = request.simpleBody.reduce((obj, item) => {
                                                                if (item.key) obj[item.key] = item.value;
                                                                return obj;
                                                            }, {});
                                                            request.body = JSON.stringify(unflattenObject(simpleBodyObject), null, 2);
                                                            showNotification('JSON data converted to Raw Text format', 'success', 2000);
                                                        } else if (!request.body.trim()) {
                                                            // If no data exists, provide a default empty JSON structure
                                                            request.body = '{\n\n}';
                                                        }
                                                    } else if (value === 'json') { // Switching to JSON (Key-Value)
                                                        // If the advanced body has content, try to parse it.
                                                        if (request.body.trim()) {
                                                            try {
                                                                const repairedJSON = jsonRepair(request.body);
                                                                const parsedBody = JSON.parse(repairedJSON);
                                                                const flattened = flattenObject(parsedBody);
                                                                request.simpleBody = Object.entries(flattened).map(([key, value]) => ({ key, value: String(value) }));
                                                                showNotification('Raw Text converted to JSON Key-Value format', 'success', 2000);
                                                            } catch (e) {
                                                                // If parsing fails, show an error but still switch the view
                                                                showNotification('Invalid JSON in raw text body. Could not parse. Starting with empty fields.', 'error', 3000);
                                                                request.simpleBody = [{ key: '', value: '' }]; // Reset to a blank state
                                                            }
                                                        } else {
                                                            // If raw body is empty, just ensure simple body isn't null
                                                            if (!request.simpleBody || request.simpleBody.length === 0) {
                                                                request.simpleBody = [{ key: '', value: '' }];
                                                            }
                                                        }
                                                    }
                                                    render();
                                                } else if (shouldRender) {
                                                    render(); // Re-render on other state changes
                                                }

                                                // Only save state for specific operations that need it immediately
                                                if (isSwitchingBodyType || shouldRender) {
                                                    saveState();
                                                }
                                            }

    /**
     * Checks Chrome storage usage and available space
     * @async
     * @function checkStorageUsage
     * @returns {Promise<{used: number, available: number, usedMB: number, availableMB: number}>}
     */
    async function checkStorageUsage() {
                                                try {
                                                    const bytesInUse = await chrome.storage.local.getBytesInUse();
                                                    const maxBytes = chrome.storage.local.QUOTA_BYTES || 10 * 1024 * 1024; // 10MB default
                                                    const available = maxBytes - bytesInUse;

                                                    return {
                                                        used: bytesInUse,
                                                        available: available,
                                                        usedMB: bytesInUse / (1024 * 1024),
                                                        availableMB: available / (1024 * 1024)
                                                    };
                                                } catch (error) {
                                                    console.error('Failed to check storage usage:', error);
                                                    return { used: 0, available: 0, usedMB: 0, availableMB: 0 };
                                                }
                                            }

    /**
     * Cleans up orphaned temporary files from storage
     * @async
     * @function cleanupOrphanedTempFiles
     * @param {number} [maxAgeMinutes=30] - Maximum age for temp files in minutes
     * @returns {Promise<number>} Number of cleaned up files
     */
    async function cleanupOrphanedTempFiles(maxAgeMinutes = 30) {
                                                try {
                                                    const allItems = await chrome.storage.local.get(null);
                                                    const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                                                    const currentTime = Date.now();
                                                    const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
                                                    let cleanedCount = 0;

                                                    console.log(`Found ${tempFileKeys.length} temporary file entries`);

                                                    for (const key of tempFileKeys) {
                                                        // Extract timestamp from key (temp_files_timestamp)
                                                        const timestampStr = key.replace('temp_files_', '');
                                                        const timestamp = parseInt(timestampStr, 10);

                                                        if (!isNaN(timestamp)) {
                                                            const age = currentTime - timestamp;
                                                            if (age > maxAge) {
                                                                await chrome.storage.local.remove(key);
                                                                cleanedCount++;
                                                                console.log(`Cleaned up orphaned temp file: ${key} (age: ${Math.round(age / 60000)} minutes)`);
                                                            }
                                                        } else {
                                                            // Invalid timestamp, clean it up
                                                            await chrome.storage.local.remove(key);
                                                            cleanedCount++;
                                                            console.log(`Cleaned up invalid temp file key: ${key}`);
                                                        }
                                                    }

                                                    if (cleanedCount > 0) {
                                                        console.log(`Storage cleanup: Removed ${cleanedCount} orphaned temporary files`);
                                                        showNotification(`Storage cleanup: Removed ${cleanedCount} old temporary files`, 'success', 3000);
                                                    }

                                                    return cleanedCount;
                                                } catch (error) {
                                                    console.error('Failed to cleanup orphaned temp files:', error);
                                                    return 0;
                                                }
                                            }

    /**
     * Emergency storage cleanup - removes ALL temporary files regardless of age
     * @async
     * @function emergencyStorageCleanup
     * @returns {Promise<{cleaned: number, freedMB: number, success: boolean}>}
     */
    async function emergencyStorageCleanup() {
                                                try {
                                                    console.log('🚨 Starting emergency storage cleanup...');

                                                    // Get all storage items
                                                    const allItems = await chrome.storage.local.get(null);
                                                    const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                                                    const uploadStateKeys = Object.keys(allItems).filter(key => key === 'uploadState');

                                                    let cleanedCount = 0;
                                                    let estimatedFreedBytes = 0;

                                                    console.log(`Found ${tempFileKeys.length} temporary file entries for emergency cleanup`);

                                                    // Remove ALL temp files (regardless of age)
                                                    for (const key of tempFileKeys) {
                                                        try {
                                                            const item = allItems[key];
                                                            if (item && Array.isArray(item)) {
                                                                // Estimate size of data being removed
                                                                estimatedFreedBytes += JSON.stringify(item).length;
                                                            }
                                                            await chrome.storage.local.remove(key);
                                                            cleanedCount++;
                                                            console.log(`Emergency cleanup: Removed temp file: ${key}`);
                                                        } catch (removeError) {
                                                            console.warn(`Failed to remove temp file ${key}:`, removeError);
                                                        }
                                                    }

                                                    // Clear any stale upload states
                                                    for (const key of uploadStateKeys) {
                                                        try {
                                                            await chrome.storage.local.remove(key);
                                                            console.log('Emergency cleanup: Cleared upload state');
                                                        } catch (removeError) {
                                                            console.warn('Failed to clear upload state:', removeError);
                                                        }
                                                    }

                                                    // Also clear any potential orphaned data
                                                    const suspiciousKeys = Object.keys(allItems).filter(key =>
                                                        key.includes('temp_') ||
                                                        key.includes('upload_') ||
                                                        (key.length > 20 && !['requests', 'settings', 'theme', 'sortBy', 'panelLeftWidth', 'fullScreenMode'].includes(key))
                                                    );

                                                    for (const key of suspiciousKeys) {
                                                        try {
                                                            await chrome.storage.local.remove(key);
                                                            cleanedCount++;
                                                            console.log(`Emergency cleanup: Removed suspicious key: ${key}`);
                                                        } catch (removeError) {
                                                            console.warn(`Failed to remove suspicious key ${key}:`, removeError);
                                                        }
                                                    }

                                                    const freedMB = estimatedFreedBytes / (1024 * 1024);
                                                    console.log(`🧹 Emergency cleanup completed: Removed ${cleanedCount} items, freed ~${freedMB.toFixed(2)}MB`);

                                                    return {
                                                        cleaned: cleanedCount,
                                                        freedMB: freedMB,
                                                        success: true
                                                    };
                                                } catch (error) {
                                                    console.error('Emergency storage cleanup failed:', error);
                                                    return {
                                                        cleaned: 0,
                                                        freedMB: 0,
                                                        success: false,
                                                        error: error.message
                                                    };
                                                }
                                            }

    /**
     * Handles manual storage cleanup initiated by user
     * @async
     * @function handleManualStorageCleanup
     * @returns {Promise<void>}
     */
    async function handleManualStorageCleanup() {
                                                try {
                                                    showNotification('🧹 Starting storage cleanup...', 'info', 2000);

                                                    // Check storage before cleanup
                                                    const usageBefore = await checkStorageUsage();
                                                    console.log(`Storage before cleanup: ${usageBefore.usedMB.toFixed(2)}MB used`);

                                                    // Run emergency cleanup
                                                    const result = await emergencyStorageCleanup();

                                                    if (result.success) {
                                                        // Check storage after cleanup
                                                        const usageAfter = await checkStorageUsage();
                                                        const actualFreed = usageBefore.usedMB - usageAfter.usedMB;

                                                        if (result.cleaned > 0) {
                                                            showNotification(
                                                                `✅ Storage cleanup successful! Removed ${result.cleaned} items, freed ${actualFreed.toFixed(2)}MB. ` +
                                                                `Storage: ${usageAfter.usedMB.toFixed(1)}MB/${(usageAfter.usedMB + usageAfter.availableMB).toFixed(1)}MB`,
                                                                'success',
                                                                8000
                                                            );
                                                        } else {
                                                            showNotification(
                                                                `ℹ️ Storage was already clean. Current usage: ${usageAfter.usedMB.toFixed(1)}MB/${(usageAfter.usedMB + usageAfter.availableMB).toFixed(1)}MB`,
                                                                'info',
                                                                5000
                                                            );
                                                        }
                                                    } else {
                                                        showNotification(
                                                            `❌ Storage cleanup failed: ${result.error || 'Unknown error'}`,
                                                            'error',
                                                            8000
                                                        );
                                                    }
                                                } catch (error) {
                                                    console.error('Manual storage cleanup error:', error);
                                                    /**
                                                     * Handles storage quota exceeded errors with recovery strategies
                                                     * @async
                                                     * @function handleStorageQuotaExceeded
                                                     * @param {Error} error - The quota exceeded error
                                                     * @returns {Promise<boolean>} True if recovery was successful
                                                     */
                                                    async function handleStorageQuotaExceeded(error) {
                                                        console.warn('Storage quota exceeded, attempting recovery...', error);

                                                        // Strategy 1: Emergency cleanup - remove ALL temporary files
                                                        console.log('Attempting emergency storage cleanup...');
                                                        const emergencyResult = await emergencyStorageCleanup();

                                                        if (emergencyResult.success && emergencyResult.cleaned > 0) {
                                                            showNotification(
                                                                `🧹 Emergency cleanup: Freed ${emergencyResult.freedMB.toFixed(2)}MB by removing ${emergencyResult.cleaned} temporary files`,
                                                                'success',
                                                                6000
                                                            );
                                                            return true;
                                                        }

                                                        // Strategy 2: Regular orphaned file cleanup
                                                        const cleanedFiles = await cleanupOrphanedTempFiles(5); // Clean files older than 5 minutes

                                                        if (cleanedFiles > 0) {
                                                            showNotification(`🧹 Storage cleanup: Freed space by removing ${cleanedFiles} temporary files`, 'success', 5000);
                                                            return true;
                                                        }

                                                        // Strategy 3: Check storage usage and provide guidance
                                                        const usage = await checkStorageUsage();
                                                        console.log(`Storage usage: ${usage.usedMB.toFixed(2)}MB used, ${usage.availableMB.toFixed(2)}MB available`);

                                                        if (usage.usedMB > 8) {
                                                            showNotification(
                                                                `⚠️ Storage critically full (${usage.usedMB.toFixed(1)}MB/${(usage.usedMB + usage.availableMB).toFixed(1)}MB). ` +
                                                                'Consider removing large files or clearing old requests.',
                                                                'warning',
                                                                8000
                                                            );
                                                            return false;
                                                        }

                                                        // Strategy 4: Force aggressive cleanup of recent files
                                                        const aggressiveCleanup = await cleanupOrphanedTempFiles(1); // Clean files older than 1 minute
                                                        if (aggressiveCleanup > 0) {
                                                            showNotification(`🆘 Emergency cleanup: Freed space by removing ${aggressiveCleanup} recent temporary files`, 'warning', 5000);
                                                            return true;
                                                        }

                                                        showNotification(
                                                            '❌ Storage quota exceeded and recovery failed. Try using the manual cleanup button or remove large files.',
                                                            'error',
                                                            10000
                                                        );
                                                        return false;
                                                    }

                                                    /**
                                                     * Saves current state to Chrome storage with quota management
                                                     * @function saveState
                                                     * @param {boolean} [showNotificationFlag=false] - Whether to show save notification
                                                     */
                                                    async function saveState(showNotificationFlag = false) {
                                                        try {
                                                            // Check storage usage before attempting to save
                                                            const usage = await checkStorageUsage();
                                                            if (usage.availableMB < 1) { // Less than 1MB available
                                                                console.warn(`Low storage space: ${usage.availableMB.toFixed(2)}MB available`);
                                                                const recovered = await handleStorageQuotaExceeded(new Error('Low storage space'));
                                                                if (!recovered) {
                                                                    return; // Don't attempt to save if recovery failed
                                                                }
                                                            }

                                                            // Create a safe copy of state for serialization
                                                            const safeState = {
                                                                requests: state.requests.map(request => {
                                                                    const safeRequest = { ...request };

                                                                    // Handle form-data files safely
                                                                    if (safeRequest.formDataBody) {
                                                                        safeRequest.formDataBody = safeRequest.formDataBody.map(item => {
                                                                            const safeItem = { ...item };

                                                                            // Check if the file data is too large for Chrome storage
                                                                            if (safeItem.value && typeof safeItem.value === 'string' && safeItem.value.startsWith('data:')) {
                                                                                const sizeInBytes = safeItem.value.length;
                                                                                const sizeInMB = sizeInBytes / (1024 * 1024);

                                                                                // Chrome storage has limits, warn if file is very large
                                                                                if (sizeInMB > 5) {
                                                                                    console.warn(`Large file detected: ${safeItem.fileName || 'Unknown'} (${formatFileSize(sizeInBytes)}). This may affect performance.`);
                                                                                }

                                                                                // If extremely large (>10MB), check if it's a fresh upload
                                                                                if (sizeInMB > 10) {
                                                                                    if (safeItem.isFreshUpload) {
                                                                                        // Fresh upload - skip storage completely, keep in memory only
                                                                                        console.log(`Fresh large file upload: ${safeItem.fileName || 'Unknown'}. Keeping in memory for immediate sending.`);
                                                                                        return null; // Remove from storage copy
                                                                                    } else {
                                                                                        // Old large file - convert to placeholder
                                                                                        console.warn(`File too large for storage: ${safeItem.fileName || 'Unknown'}. Storing metadata only.`);
                                                                                        safeItem.value = `[LARGE_FILE_PLACEHOLDER]_${safeItem.fileName}_${safeItem.fileSize}`;
                                                                                        safeItem.isPlaceholder = true;
                                                                                    }
                                                                                }
                                                                            }

                                                                            return safeItem;
                                                                        }).filter(item => item !== null); // Remove null entries (fresh uploads)
                                                                    }

                                                                    return safeRequest;
                                                                }),
                                                                settings: state.settings
                                                            };

                                                            // Estimate data size
                                                            const dataString = JSON.stringify(safeState);
                                                            const dataSizeInMB = dataString.length / (1024 * 1024);

                                                            if (dataSizeInMB > 8) { // Chrome storage limit is around 10MB for local storage
                                                                console.warn(`Large data size: ${dataSizeInMB.toFixed(2)}MB. Consider removing large files.`);
                                                                showNotification('Warning: Data size is large. Consider removing large files to improve performance.', 'warning', 5000);
                                                            }

                                                            // Save the state to local storage with quota error handling
                                                            try {
                                                                await chrome.storage.local.set(safeState);
                                                                if (showNotificationFlag) {
                                                                    showNotification('Request Saved!', 'success');
                                                                }
                                                            } catch (saveError) {
                                                                if (saveError.message && saveError.message.includes('quota')) {
                                                                    console.error('Storage quota exceeded during save:', saveError);
                                                                    const recovered = await handleStorageQuotaExceeded(saveError);
                                                                    if (recovered) {
                                                                        // Retry save after cleanup
                                                                        await chrome.storage.local.set(safeState);
                                                                        showNotification('Request saved after storage cleanup!', 'success');
                                                                    } else {
                                                                        showNotification('Failed to save: Storage quota exceeded. Please remove large files.', 'error', 8000);
                                                                    }
                                                                } else {
                                                                    throw saveError; // Re-throw non-quota errors
                                                                }
                                                            }
                                                        } catch (error) {
                                                            console.error('Error saving state:', error.message, error);
                                                            if (error.message && error.message.includes('quota')) {
                                                                showNotification('Storage quota exceeded. Please remove large files or clear extension data.', 'error', 8000);
                                                            } else {
                                                                showNotification('Failed to save data: ' + error.message, 'error', 5000);
                                                            }
                                                        }
                                                    }

                                                    let notificationTimeout = null;
                                                    const notificationMessageEl = document.getElementById('notification-message');
                                                    const notificationActionsEl = document.getElementById('notification-actions');

                                                    function showNotification(message, type = 'success', duration = 3000, actions = []) {
                                                        clearTimeout(notificationTimeout);

                                                        notificationMessageEl.textContent = message;
                                                        notificationBarEl.className = `notification-bar ${type}`;

                                                        // Clear previous actions
                                                        notificationActionsEl.innerHTML = '';

                                                        // Add new action buttons
                                                        if (actions.length > 0) {
                                                            const actionsContainer = document.createElement('div');
                                                            actions.forEach(action => {
                                                                const button = document.createElement('button');
                                                                button.textContent = action.text;
                                                                button.onclick = () => {
                                                                    action.onClick();
                                                                    notificationBarEl.classList.add('hidden');
                                                                };
                                                                actionsContainer.appendChild(button);
                                                                notificationActionsEl.appendChild(actionsContainer);
                                                            }

                notificationBarEl.classList.remove('hidden');

                                                            // Auto-hide if duration is set
                                                            if (duration) {
                                                                notificationTimeout = setTimeout(() => {
                                                                    notificationBarEl.classList.add('hidden');
                                                                }, duration);
                                                            }
                                                        }

                                                        function clearValidation() {
                                                            document.querySelectorAll('.invalid-field').forEach(el => el.classList.remove('invalid-field'));
                                                        }

                                                        // --- Event Listeners ---
                                                        const keepDataCheckbox = document.getElementById('keep-data-checkbox');
                                                        const keepDataLabel = document.querySelector('label[for="keep-data-checkbox"]');
                                                        keepDataLabel.textContent = 'Keep data 💾';

                                                        const cleanJsonLabel = document.querySelector('label[for="extreme-clean-checkbox"]');
                                                        cleanJsonLabel.textContent = 'Clean JSON 🧹';

                                                        keepDataCheckbox.onchange = (e) => {
                                                            const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                            if (request) {
                                                                request.keepData = e.target.checked;
                                                                saveState();
                                                                showNotification(`Keep Data ${e.target.checked ? 'Enabled' : 'Disabled'}`, 'success');
                                                            }
                                                        };

                                                        extremeCleanCheckbox.onchange = (e) => {
                                                            const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                            if (request) {
                                                                request.extremeClean = e.target.checked;
                                                                saveState();
                                                                showNotification(`Clean JSON ${e.target.checked ? 'Enabled' : 'Disabled'}`, 'success');
                                                            }
                                                        };
                                                        searchInputEl.oninput = (e) => {
                                                            state.searchQuery = e.target.value;
                                                            render();
                                                        };
                                                        requestNameInput.addEventListener('focus', (e) => {
                                                            originalRequestName = e.target.value;
                                                            saveNameBtn.classList.add('hidden'); // Ensure it's hidden on focus

                                                            requestNameInput.addEventListener('input', (e) => {
                                                                if (e.target.value !== originalRequestName) {
                                                                    saveNameBtn.classList.remove('hidden');
                                                                } else {
                                                                    saveNameBtn.classList.add('hidden');
                                                                }

                                                                requestNameInput.addEventListener('blur', (e) => {
                                                                    // If the button is visible, it means there are unsaved changes.
                                                                    // A short timeout allows the button click to register before the blur reverts the name.
                                                                    setTimeout(() => {
                                                                        if (!saveNameBtn.classList.contains('hidden')) {
                                                                            e.target.value = originalRequestName;
                                                                            saveNameBtn.classList.add('hidden');
                                                                        }
                                                                    }, 200);

                                                                    saveNameBtn.addEventListener('click', () => {
                                                                        const newName = requestNameInput.value.trim();
                                                                        if (newName) {
                                                                            updateState('name', newName, null, true); // Re-render to update the list
                                                                            originalRequestName = newName;
                                                                            saveNameBtn.classList.add('hidden');
                                                                            showNotification('Name saved!', 'success');
                                                                        } else {
                                                                            requestNameInput.value = originalRequestName;
                                                                            showNotification('Name cannot be empty.', 'error');
                                                                        }
                                                                        urlInput.oninput = (e) => {
                                                                            e.target.classList.remove('invalid-field');
                                                                            updateState('url', e.target.value);
                                                                        };
                                                                        httpMethodSelect.onchange = (e) => updateState('method', e.target.value);
                                                                        bodyTypeSelect.onchange = (e) => updateState('bodyType', e.target.value);
                                                                        bodyInput.oninput = (e) => updateState('body', e.target.value);


                                                                        headersContainerEl.oninput = (e) => {
                                                                            if (e.target.matches('.header-key, .header-value')) {
                                                                                e.target.classList.remove('invalid-field');
                                                                                const removeBtn = e.target.parentElement.querySelector('.remove-header-btn');
                                                                                if (!removeBtn) return; // Safety check
                                                                                const index = removeBtn.dataset.index;
                                                                                const field = e.target.className;
                                                                                updateState('headers.' + (field === 'header-key' ? 'key' : 'value'), e.target.value, index);

                                                                                // Auto-save data immediately for persistence
                                                                                saveState();
                                                                            }
                                                                        };

                                                                        simpleBodyContainerEl.oninput = (e) => {
                                                                            // Handle auto-sizing for textareas as the user types
                                                                            if (e.target.matches('.simple-body-key, .simple-body-value')) {
                                                                                e.target.style.height = 'auto';
                                                                                e.target.style.height = (e.target.scrollHeight) + 'px';

                                                                                e.target.classList.remove('invalid-field');
                                                                                const removeBtn = e.target.parentElement.querySelector('.remove-simple-row-btn');
                                                                                if (!removeBtn) return; // Safety check
                                                                                const index = removeBtn.dataset.index;
                                                                                const field = e.target.className;
                                                                                // Update state without triggering a full re-render but ensure it's saved
                                                                                updateState('simpleBody.' + (field.includes('key') ? 'key' : 'value'), e.target.value, index, false);

                                                                                // Auto-save data immediately for persistence
                                                                                saveState();
                                                                            }
                                                                        };

                                                                        // Expand value textarea on focus
                                                                        simpleBodyContainerEl.addEventListener('focusin', (e) => {
                                                                            if (e.target.matches('.simple-body-key, .simple-body-value')) {
                                                                                e.target.classList.add('expanded');
                                                                            }
                                                                            if (e.target.matches('.simple-body-value')) {
                                                                                const text = e.target.value;
                                                                                const charCount = text.length;
                                                                                const wordCount = text.trim().split(/\s+/).filter(Boolean).length;
                                                                                charCountEl.textContent = charCount;
                                                                                wordCountEl.textContent = wordCount;
                                                                                valueCharWordCountEl.classList.remove('hidden');
                                                                            }

                                                                            // Shrink value textarea on blur
                                                                            simpleBodyContainerEl.addEventListener('focusout', (e) => {
                                                                                if (e.target.matches('.simple-body-key, .simple-body-value')) {
                                                                                    // Check if the new focused element is the send button. If so, do nothing.
                                                                                    if (e.relatedTarget === sendBtn) {
                                                                                        return;
                                                                                    }
                                                                                    e.target.classList.remove('expanded');
                                                                                    // Remove any inline height style set by manual resizing
                                                                                    e.target.style.height = '';
                                                                                }

                                                                                if (e.target.matches('.simple-body-value')) {
                                                                                    valueCharWordCountEl.classList.add('hidden');
                                                                                }

                                                                                simpleBodyContainerEl.addEventListener('input', (e) => {
                                                                                    if (e.target.matches('.simple-body-value')) {
                                                                                        const text = e.target.value;
                                                                                        const charCount = text.length;
                                                                                        const wordCount = text.trim().split(/\s+/).filter(Boolean).length;
                                                                                        charCountEl.textContent = charCount;
                                                                                        wordCountEl.textContent = wordCount;
                                                                                    }


                                                                                    // --- Action Buttons & Dynamic Rows ---
                                                                                    addHeaderBtn.onclick = () => {
                                                                                        const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                        if (request) {
                                                                                            request.headers.push({ key: '', value: '' });
                                                                                            saveState();
                                                                                            render();
                                                                                        }
                                                                                    };

                                                                                    addSimpleRowBtn.onclick = () => {
                                                                                        const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                        if (request) {
                                                                                            request.simpleBody.push({ key: '', value: '' });
                                                                                            saveState();
                                                                                            render();
                                                                                        }
                                                                                    };

                                                                                    addFormDataRowBtn.onclick = () => {
                                                                                        const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                        if (request) {
                                                                                            if (!request.formDataBody) {
                                                                                                request.formDataBody = [];
                                                                                            }
                                                                                            // Default to 'file' when adding a new row in the form-data section.
                                                                                            request.formDataBody.push({ type: 'file', key: '', value: '' });
                                                                                            saveState();
                                                                                            render();
                                                                                        }
                                                                                    };

                                                                                    function handleFieldDelete(type, e) {
                                                                                        if (!e.target.matches(`.remove-${type}-btn`)) return;

                                                                                        if (!e.target.dataset) return; // Safety check
                                                                                        const index = parseInt(e.target.dataset.index, 10);
                                                                                        const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                        if (!request) return;

                                                                                        clearTimeout(deleteFieldTimeout);

                                                                                        if (deleteFieldConfirmState.type === type && deleteFieldConfirmState.index === index) {
                                                                                            // This is the second click, confirm delete
                                                                                            if (type === 'header') {
                                                                                                request.headers.splice(index, 1);
                                                                                            } else if (type === 'simple-row') {
                                                                                                request.simpleBody.splice(index, 1);
                                                                                            } else if (type === 'form-data-row') {
                                                                                                request.formDataBody.splice(index, 1);
                                                                                            }
                                                                                            deleteFieldConfirmState = { type: null, index: null };
                                                                                            saveState();
                                                                                            render();
                                                                                        } else {
                                                                                            // This is the first click, enter confirmation state
                                                                                            // If another button is already in confirm state, reset it first without a full render
                                                                                            if (deleteFieldConfirmState.type) {
                                                                                                let container;
                                                                                                if (deleteFieldConfirmState.type === 'header') {
                                                                                                    container = headersContainerEl;
                                                                                                } else if (deleteFieldConfirmState.type === 'simple-row') {
                                                                                                    container = simpleBodyContainerEl;
                                                                                                } else {
                                                                                                    container = formDataContainerEl;
                                                                                                }
                                                                                                const oldButton = container.querySelector(`.remove-${deleteFieldConfirmState.type}-btn[data-index="${deleteFieldConfirmState.index}"]`);
                                                                                                if (oldButton) {
                                                                                                    oldButton.innerHTML = '-';
                                                                                                    oldButton.style.backgroundColor = '';
                                                                                                }
                                                                                            }

                                                                                            deleteFieldConfirmState = { type, index };

                                                                                            const button = e.target;
                                                                                            button.innerHTML = '&#10003;'; // Checkmark
                                                                                            button.style.backgroundColor = 'var(--success-color)';

                                                                                            deleteFieldTimeout = setTimeout(() => {
                                                                                                button.innerHTML = '-';
                                                                                                button.style.backgroundColor = ''; // Revert to CSS color
                                                                                                deleteFieldConfirmState = { type: null, index: null };
                                                                                            }, 3000);
                                                                                        }
                                                                                    }

                                                                                    // Use more specific event delegation for clear buttons
                                                                                    document.addEventListener('click', (e) => {
                                                                                        // Handle simple body clear buttons
                                                                                        if (e.target.closest('.clear-value-btn')) {
                                                                                            e.preventDefault();
                                                                                            e.stopPropagation();
                                                                                            e.stopImmediatePropagation(); // Prevent any other handlers

                                                                                            const clearBtn = e.target.closest('.clear-value-btn');
                                                                                            if (!clearBtn || !clearBtn.dataset) return; // Safety check
                                                                                            const index = parseInt(clearBtn.dataset.index, 10);
                                                                                            const wrapper = clearBtn.parentElement;

                                                                                            // Determine which type of field this is
                                                                                            const textarea = wrapper.querySelector('.simple-body-value');
                                                                                            const formDataTextarea = wrapper.querySelector('.form-data-value');

                                                                                            if (textarea) {
                                                                                                // Simple body field
                                                                                                handleFieldClear(wrapper, textarea, 'simpleBody.value', index, 'Value cleared');
                                                                                            } else if (formDataTextarea) {
                                                                                                // Form-data text field
                                                                                                handleFieldClear(wrapper, formDataTextarea, 'formDataBody.value', index, 'Form data value cleared');
                                                                                            }

                                                                                            return false; // Ensure no further propagation
                                                                                        }

                                                                                        // Handle header clear buttons
                                                                                        if (e.target.closest('.clear-header-value-btn')) {
                                                                                            e.preventDefault();
                                                                                            e.stopPropagation();
                                                                                            e.stopImmediatePropagation();

                                                                                            const clearBtn = e.target.closest('.clear-header-value-btn');
                                                                                            if (!clearBtn || !clearBtn.dataset) return; // Safety check
                                                                                            const index = parseInt(clearBtn.dataset.index, 10);
                                                                                            const wrapper = clearBtn.parentElement;
                                                                                            const input = wrapper.querySelector('.header-value');

                                                                                            if (input) {
                                                                                                handleFieldClear(wrapper, input, 'headers.value', index, 'Header value cleared');
                                                                                            }
                                                                                            return false;
                                                                                        }
                                                                                    }, true); // Use capture phase to handle before textarea focus events

                                                                                    /**
                                                                                     * Helper function to handle clearing field values and maintaining expanded state
                                                                                     * @function handleFieldClear
                                                                                     * @param {HTMLElement} wrapper - The wrapper element containing the field
                                                                                     * @param {HTMLElement} field - The input/textarea field to clear
                                                                                     * @param {string} stateKey - The state key to update
                                                                                     * @param {number} index - The index of the field
                                                                                     * @param {string} message - Success message to show
                                                                                     */
                                                                                    function handleFieldClear(wrapper, field, stateKey, index, message) {
                                                                                        const wasExpanded = field.classList.contains('expanded');
                                                                                        updateState(stateKey, '', index, false);
                                                                                        field.value = '';
                                                                                        wrapper.classList.remove('has-content');

                                                                                        // Maintain expanded state if it was expanded
                                                                                        if (wasExpanded) {
                                                                                            setTimeout(() => {
                                                                                                field.focus();
                                                                                                field.classList.add('expanded');
                                                                                            }, 10);
                                                                                        }

                                                                                        showNotification(message, 'success', 1500);
                                                                                    }

                                                                                    /**
                                                                                     * Function to toggle clear button visibility based on content
                                                                                     * @function toggleClearButton
                                                                                     * @param {HTMLElement} element - The input/textarea element
                                                                                     */
                                                                                    function toggleClearButton(element) {
                                                                                        const wrapper = element.closest('.value-input-wrapper');
                                                                                        if (wrapper) {
                                                                                            const hasContent = element.value.trim().length > 0;
                                                                                            wrapper.classList.toggle('has-content', hasContent);
                                                                                        }
                                                                                    }

                                                                                    // Add input event listeners to manage clear button visibility
                                                                                    document.addEventListener('input', (e) => {
                                                                                        if (e.target.matches('.simple-body-value, .header-value, .form-data-value')) {
                                                                                            toggleClearButton(e.target);
                                                                                        }

                                                                                        // Add focus event listeners to ensure clear buttons appear when focusing on fields with content
                                                                                        document.addEventListener('focus', (e) => {
                                                                                            if (e.target.matches('.simple-body-value, .header-value, .form-data-value')) {
                                                                                                toggleClearButton(e.target);
                                                                                            }
                                                                                        }, true);

                                                                                        headersContainerEl.onclick = (e) => handleFieldDelete('header', e);
                                                                                        simpleBodyContainerEl.onclick = (e) => {
                                                                                            const clearBtn = e.target.closest('.clear-value-btn');
                                                                                            if (clearBtn) {
                                                                                                // This will be handled by the document-level event listener
                                                                                                return;
                                                                                            } else {
                                                                                                handleFieldDelete('simple-row', e);
                                                                                            }
                                                                                        };

                                                                                        // Add focus/blur listeners for form-data textareas
                                                                                        formDataContainerEl.addEventListener('focusin', (e) => {
                                                                                            if (e.target.matches('.form-data-key, .form-data-value')) {
                                                                                                e.target.classList.add('expanded');
                                                                                            }

                                                                                            formDataContainerEl.addEventListener('focusout', (e) => {
                                                                                                if (e.target.matches('.form-data-key, .form-data-value')) {
                                                                                                    e.target.classList.remove('expanded');
                                                                                                    e.target.style.height = ''; // Reset manual resize on blur
                                                                                                }


                                                                                                formDataContainerEl.onclick = (e) => {
                                                                                                    const clearFileBtn = e.target.closest('.clear-file-btn');
                                                                                                    if (clearFileBtn && clearFileBtn.dataset) {
                                                                                                        const index = clearFileBtn.dataset.index;
                                                                                                        const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                                        if (request && request.formDataBody[index]) {
                                                                                                            const wasLargeFile = request.formDataBody[index].fileSize > 10 * 1024 * 1024;
                                                                                                            const wasFreshUpload = request.formDataBody[index].isFreshUpload;

                                                                                                            // Clear all file-related data
                                                                                                            request.formDataBody[index].value = '';
                                                                                                            request.formDataBody[index].fileName = '';
                                                                                                            request.formDataBody[index].fileSize = 0;
                                                                                                            request.formDataBody[index].fileType = '';
                                                                                                            request.formDataBody[index].isFreshUpload = false;
                                                                                                            request.formDataBody[index].isPlaceholder = false;

                                                                                                            // Save state and re-render
                                                                                                            saveState();
                                                                                                            render();

                                                                                                            // Show appropriate notification
                                                                                                            if (wasFreshUpload && wasLargeFile) {
                                                                                                                showNotification('🗑️ Large file cleared. Ready for new upload.', 'success', 2000);
                                                                                                            } else {
                                                                                                                showNotification('🗑️ File cleared', 'success', 1500);
                                                                                                            }
                                                                                                        }
                                                                                                    } else if (e.target.matches('.remove-form-data-row-btn')) {
                                                                                                        handleFieldDelete('form-data-row', e);
                                                                                                    }
                                                                                                };

                                                                                                formDataContainerEl.addEventListener('input', (e) => {
                                                                                                    const itemEl = e.target.closest('.form-data-item');
                                                                                                    if (!itemEl) return;

                                                                                                    const removeBtn = itemEl.querySelector('.remove-form-data-row-btn');
                                                                                                    if (!removeBtn) return; // Safety check
                                                                                                    const index = removeBtn.dataset.index;

                                                                                                    // Handle auto-sizing for textareas as the user types
                                                                                                    if (e.target.matches('.form-data-key, .form-data-value')) {
                                                                                                        e.target.style.height = 'auto';
                                                                                                        e.target.style.height = (e.target.scrollHeight) + 'px';
                                                                                                    }

                                                                                                    if (e.target.matches('.form-data-key')) {
                                                                                                        // State update logic
                                                                                                        updateState('formDataBody.key', e.target.value, index, false);
                                                                                                    } else if (e.target.matches('.form-data-value')) {
                                                                                                        // State update logic
                                                                                                        updateState('formDataBody.value', e.target.value, index, false);
                                                                                                    }

                                                                                                    // Auto-save data immediately for persistence
                                                                                                    saveState();

                                                                                                    formDataContainerEl.addEventListener('change', (e) => {
                                                                                                        if (e.target.matches('.form-data-type')) {
                                                                                                            if (!e.target.dataset) return; // Safety check
                                                                                                            const index = e.target.dataset.index;
                                                                                                            const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                                            if (request && request.formDataBody[index]) {
                                                                                                                request.formDataBody[index].type = e.target.value;
                                                                                                                if (e.target.value === 'text' && request.formDataBody[index].value === undefined) {
                                                                                                                    request.formDataBody[index].value = '';
                                                                                                                }
                                                                                                                saveState();
                                                                                                                render();
                                                                                                            }
                                                                                                        } else if (e.target.matches('.form-data-file')) {
                                                                                                            if (!e.target.dataset) return; // Safety check
                                                                                                            const index = e.target.dataset.index;
                                                                                                            const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                                            if (request && request.formDataBody[index]) {
                                                                                                                const file = e.target.files[0];
                                                                                                                if (file) {
                                                                                                                    // Show upload progress
                                                                                                                    const uploadArea = e.target.closest('.file-upload-area');
                                                                                                                    const progressContainer = uploadArea?.querySelector('.upload-progress');
                                                                                                                    const progressFill = uploadArea?.querySelector('.progress-fill');
                                                                                                                    const progressText = uploadArea?.querySelector('.progress-text');

                                                                                                                    if (progressContainer) {
                                                                                                                        progressContainer.classList.remove('hidden');
                                                                                                                        uploadArea.querySelector('.upload-content')?.classList.add('hidden');
                                                                                                                    }

                                                                                                                    const reader = new FileReader();

                                                                                                                    // Progress tracking
                                                                                                                    reader.onprogress = (event) => {
                                                                                                                        if (event.lengthComputable && progressFill && progressText) {
                                                                                                                            const percentComplete = (event.loaded / event.total) * 100;
                                                                                                                            progressFill.style.width = percentComplete + '%';
                                                                                                                            progressText.textContent = `Uploading... ${Math.round(percentComplete)}%`;
                                                                                                                        }
                                                                                                                    };

                                                                                                                    reader.onload = (event) => {
                                                                                                                        const fileSize = file.size;
                                                                                                                        const fileSizeInMB = fileSize / (1024 * 1024);

                                                                                                                        request.formDataBody[index].value = event.target.result;
                                                                                                                        request.formDataBody[index].fileName = file.name;
                                                                                                                        request.formDataBody[index].fileSize = file.size; // Store file size
                                                                                                                        request.formDataBody[index].fileType = file.type; // Store MIME type

                                                                                                                        // Mark large files as fresh uploads (don't save to storage)
                                                                                                                        if (fileSizeInMB > 10) {
                                                                                                                            request.formDataBody[index].isFreshUpload = true;
                                                                                                                            request.formDataBody[index].isPlaceholder = false;
                                                                                                                        } else {
                                                                                                                            request.formDataBody[index].isFreshUpload = false;
                                                                                                                            request.formDataBody[index].isPlaceholder = false;
                                                                                                                        }

                                                                                                                        if (progressText) {
                                                                                                                            progressText.textContent = 'Upload complete!';
                                                                                                                        }

                                                                                                                        // Small delay to show completion before re-rendering
                                                                                                                        setTimeout(() => {
                                                                                                                            // Only save to storage if it's not a large file
                                                                                                                            if (fileSizeInMB <= 10) {
                                                                                                                                saveState();
                                                                                                                            }
                                                                                                                            render();

                                                                                                                            // Show enhanced success notification
                                                                                                                            const fileIcon = getFileTypeIcon(file.name.substring(file.name.lastIndexOf('.')));
                                                                                                                            const fileSize = formatFileSize(file.size);

                                                                                                                            let message = `${fileIcon} File uploaded: ${file.name} (${fileSize})`;
                                                                                                                            if (fileSizeInMB > 10) {
                                                                                                                                message += ' - Ready to send!';
                                                                                                                            }

                                                                                                                            showNotification(message, 'success', 3000);
                                                                                                                        }, 500);
                                                                                                                    };

                                                                                                                    reader.onerror = () => {
                                                                                                                        if (progressText) {
                                                                                                                            progressText.textContent = 'Upload failed!';
                                                                                                                        }
                                                                                                                        showNotification('File upload failed. Please try again.', 'error', 3000);

                                                                                                                        // Reset the upload area after a delay
                                                                                                                        setTimeout(() => {
                                                                                                                            if (progressContainer) {
                                                                                                                                progressContainer.classList.add('hidden');
                                                                                                                                uploadArea.querySelector('.upload-content')?.classList.remove('hidden');
                                                                                                                            }
                                                                                                                        }, 2000);
                                                                                                                    };

                                                                                                                    reader.readAsDataURL(file);
                                                                                                                }
                                                                                                            }
                                                                                                        }

                                                                                                        // Add drag and drop functionality for file uploads
                                                                                                        document.addEventListener('dragover', (e) => {
                                                                                                            e.preventDefault();
                                                                                                            const uploadArea = e.target.closest('.file-upload-area');
                                                                                                            if (uploadArea) {
                                                                                                                uploadArea.classList.add('drag-over');
                                                                                                            }

                                                                                                            document.addEventListener('dragleave', (e) => {
                                                                                                                e.preventDefault();
                                                                                                                const uploadArea = e.target.closest('.file-upload-area');
                                                                                                                if (uploadArea && !uploadArea.contains(e.relatedTarget)) {
                                                                                                                    uploadArea.classList.remove('drag-over');
                                                                                                                }

                                                                                                                document.addEventListener('drop', (e) => {
                                                                                                                    e.preventDefault();
                                                                                                                    const uploadArea = e.target.closest('.file-upload-area');
                                                                                                                    if (uploadArea) {
                                                                                                                        uploadArea.classList.remove('drag-over');

                                                                                                                        const index = uploadArea.dataset.index;
                                                                                                                        const files = e.dataTransfer.files;

                                                                                                                        if (files.length > 0 && index) {
                                                                                                                            const file = files[0];
                                                                                                                            const request = state.requests.find(r => r.id === state.selectedRequestId);

                                                                                                                            if (request && request.formDataBody[index]) {
                                                                                                                                // Simulate file input change for consistent handling
                                                                                                                                const fileInput = uploadArea.querySelector('.form-data-file');
                                                                                                                                if (fileInput) {
                                                                                                                                    // Create a new FileList-like object
                                                                                                                                    const dt = new DataTransfer();
                                                                                                                                    dt.items.add(file);
                                                                                                                                    fileInput.files = dt.files;

                                                                                                                                    // Trigger the change event
                                                                                                                                    const changeEvent = new Event('change', { bubbles: true });
                                                                                                                                    fileInput.dispatchEvent(changeEvent);
                                                                                                                                }
                                                                                                                            }
                                                                                                                        }
                                                                                                                    }

                                                                                                                    addNewRequestBtn.onclick = () => {
                                                                                                                        const now = Date.now();
                                                                                                                        const newRequest = {
                                                                                                                            id: `req_${now}`,
                                                                                                                            name: 'New Request',
                                                                                                                            bodyType: 'json',
                                                                                                                            method: 'POST',
                                                                                                                            url: '',
                                                                                                                            headers: [{ key: 'Content-Type', value: 'application/json' }],
                                                                                                                            simpleBody: [{ key: '', value: '' }],
                                                                                                                            formDataBody: [],
                                                                                                                            body: '',
                                                                                                                            createdAt: now,
                                                                                                                            useCount: 0,
                                                                                                                            lastUsed: 0,
                                                                                                                            keepData: false, // Per-request setting
                                                                                                                            extremeClean: false, // Per-request setting
                                                                                                                        };
                                                                                                                        state.requests.push(newRequest);
                                                                                                                        saveState();
                                                                                                                        selectRequest(newRequest.id);

                                                                                                                        // After rendering the new request, immediately highlight required fields.
                                                                                                                        // Using requestAnimationFrame ensures this runs after the DOM is painted.
                                                                                                                        requestAnimationFrame(() => {
                                                                                                                            clearValidation();
                                                                                                                            urlInput.classList.add('invalid-field');
                                                                                                                            const firstKeyInput = simpleBodyContainerEl.querySelector('.simple-body-key');
                                                                                                                            if (firstKeyInput) {
                                                                                                                                firstKeyInput.classList.add('invalid-field');
                                                                                                                            }
                                                                                                                            showNotification('Please fill in the highlighted fields.', 'info');
                                                                                                                            urlInput.focus();
                                                                                                                        };

                                                                                                                        deleteRequestBtn.onclick = () => {
                                                                                                                            if (!state.selectedRequestId) return;

                                                                                                                            const actions = [
                                                                                                                                {
                                                                                                                                    text: 'Confirm',
                                                                                                                                    onClick: () => {
                                                                                                                                        state.requests = state.requests.filter(r => r.id !== state.selectedRequestId);
                                                                                                                                        const newIdToSelect = state.requests.length > 0 ? state.requests[0].id : null;
                                                                                                                                        state.selectedRequestId = null; // Deselect first
                                                                                                                                        saveState();
                                                                                                                                        selectRequest(newIdToSelect); // Then select the new one
                                                                                                                                        showNotification('Request deleted.', 'success');
                                                                                                                                    }
                                                                                                                                },
                                                                                                                                {
                                                                                                                                    text: 'Cancel',
                                                                                                                                    onClick: () => { /* Do nothing */ }
                                                                                                                                }
                                                                                                                            ];
                                                                                                                            showNotification('Are you sure you want to delete this request?', 'confirm', null, actions);
                                                                                                                        };

                                                                                                                        sortBtn.onclick = (e) => {
                                                                                                                            e.stopPropagation(); // Prevent the document click listener from firing immediately
                                                                                                                            sortMenuEl.classList.toggle('hidden');
                                                                                                                        };

                                                                                                                        themeToggleBtn.onclick = () => {
                                                                                                                            toggleTheme();
                                                                                                                        };

                                                                                                                        clearRawBodyBtn.onclick = () => {
                                                                                                                            bodyInput.value = '';
                                                                                                                            updateState('body', '');
                                                                                                                            showNotification('Raw body cleared', 'success', 1500);
                                                                                                                        };

                                                                                                                        sortMenuEl.onclick = (e) => {
                                                                                                                            if (e.target.matches('.sort-option')) {
                                                                                                                                const sortBy = e.target.dataset.value;
                                                                                                                                state.sortBy = sortBy;
                                                                                                                                chrome.storage.session.set({ sortBy: sortBy });
                                                                                                                                sortMenuEl.classList.add('hidden');
                                                                                                                                render();
                                                                                                                            }
                                                                                                                        };

                                                                                                                        // Hide sort menu if clicking elsewhere
                                                                                                                        document.addEventListener('click', (e) => {
                                                                                                                            if (!sortMenuEl.contains(e.target) && !sortBtn.contains(e.target)) {
                                                                                                                                sortMenuEl.classList.add('hidden');
                                                                                                                            }

                                                                                                                            sendBtn.onclick = async () => {
                                                                                                                                const request = state.requests.find(r => r.id === state.selectedRequestId);
                                                                                                                                if (!request) return;

                                                                                                                                clearValidation();

                                                                                                                                // --- Primary Validation ---
                                                                                                                                // Enhanced URL validation
                                                                                                                                const isValidUrl = (url) => {
                                                                                                                                    try {
                                                                                                                                        const urlObj = new URL(url);
                                                                                                                                        return ['http:', 'https:'].includes(urlObj.protocol);
                                                                                                                                    } catch {
                                                                                                                                        return false;
                                                                                                                                    }
                                                                                                                                };

                                                                                                                                if (!request.url || !isValidUrl(request.url)) {
                                                                                                                                    showNotification('A valid HTTP/HTTPS URL is required.', 'error');
                                                                                                                                    urlInput.classList.add('invalid-field');
                                                                                                                                    urlInput.focus();
                                                                                                                                    return;
                                                                                                                                }

                                                                                                                                // --- Body Validation (Construction is now done in the background script) ---
                                                                                                                                if (request.bodyType === 'raw') {
                                                                                                                                    if (['POST', 'PUT'].includes(request.method) && !request.body.trim()) {
                                                                                                                                        showNotification('Body cannot be empty for POST/PUT requests in Raw mode.', 'error');
                                                                                                                                        return;
                                                                                                                                    }
                                                                                                                                } else if (request.bodyType !== 'form-data') { // Simple Mode Validation
                                                                                                                                    // First, ensure state is synchronized with current DOM values
                                                                                                                                    const simpleBodyRows = simpleBodyContainerEl.querySelectorAll('.simple-body-item');
                                                                                                                                    simpleBodyRows.forEach((row, index) => {
                                                                                                                                        const keyInput = row.querySelector('.simple-body-key');
                                                                                                                                        const valueInput = row.querySelector('.simple-body-value');
                                                                                                                                        if (keyInput && valueInput && request.simpleBody[index]) {
                                                                                                                                            // Update state with current DOM values to ensure synchronization
                                                                                                                                            request.simpleBody[index].key = keyInput.value;
                                                                                                                                            request.simpleBody[index].value = valueInput.value;
                                                                                                                                        }

                                                                                                                                        let hasAtLeastOneKey = false;
                                                                                                                                        let allValuesHaveKeys = true;
                                                                                                                                        for (const [index, item] of request.simpleBody.entries()) {
                                                                                                                                            const row = simpleBodyContainerEl.children[index];
                                                                                                                                            const trimmedKey = (item.key || '').trim();
                                                                                                                                            const trimmedValue = (item.value || '').trim();

                                                                                                                                            if (trimmedKey) {
                                                                                                                                                hasAtLeastOneKey = true;
                                                                                                                                                if (!trimmedValue) {
                                                                                                                                                    if (row) row.querySelector('.simple-body-value').classList.add('invalid-field');
                                                                                                                                                    showNotification(`A value is required for the key "${trimmedKey}".`, 'error');
                                                                                                                                                    return;
                                                                                                                                                }
                                                                                                                                            } else if (trimmedValue) {
                                                                                                                                                allValuesHaveKeys = false;
                                                                                                                                                if (row) row.querySelector('.simple-body-key').classList.add('invalid-field');
                                                                                                                                            }
                                                                                                                                        }
                                                                                                                                        if (!allValuesHaveKeys) {
                                                                                                                                            showNotification('A key is required for every value.', 'error');
                                                                                                                                            return;
                                                                                                                                        }
                                                                                                                                        if (!hasAtLeastOneKey) {
                                                                                                                                            showNotification('At least one Key is required to send a request.', 'error');
                                                                                                                                            const firstKeyInput = simpleBodyContainerEl.querySelector('.simple-body-key');
                                                                                                                                            if (firstKeyInput) firstKeyInput.classList.add('invalid-field');
                                                                                                                                            return;
                                                                                                                                        }
                                                                                                                                    }

                // --- UI Feedback for Sending ---
                const hasLargeFiles = request.bodyType === 'form-data' && request.formDataBody?.some(item =>
                                                                                                                                        item.type === 'file' && item.fileSize && item.fileSize > 5 * 1024 * 1024
                                                                                                                                    );

                                                                                                                                    sendBtn.classList.add('loading');
                                                                                                                                    sendBtn.disabled = true;

                                                                                                                                    // Create message with unique request ID first (to avoid ReferenceError)
                                                                                                                                    const requestId = Date.now().toString();

                                                                                                                                    // Store large file data separately to avoid message size limits
                                                                                                                                    const originalFormDataFiles = request.formDataBody ?
                                                                                                                                        request.formDataBody.filter(item => item.type === 'file')
                                                                                                                                            .map((item, index) => ({
                                                                                                                                                index: request.formDataBody.indexOf(item),
                                                                                                                                                data: item.value,
                                                                                                                                                fileName: item.fileName,
                                                                                                                                                key: item.key
                                                                                                                                            })) : [];

                                                                                                                                    // Save file data to temporary storage if there are files
                                                                                                                                    if (originalFormDataFiles.length > 0) {
                                                                                                                                        const tempFileKey = `temp_files_${requestId}`;
                                                                                                                                        try {
                                                                                                                                            await chrome.storage.local.set({ [tempFileKey]: originalFormDataFiles });
                                                                                                                                            console.log(`Stored ${originalFormDataFiles.length} files in temporary storage (${tempFileKey})`);
                                                                                                                                        } catch (storageError) {
                                                                                                                                            if (storageError.message && storageError.message.includes('quota')) {
                                                                                                                                                console.warn('Storage quota exceeded when storing temp files, attempting cleanup...');
                                                                                                                                                const recovered = await handleStorageQuotaExceeded(storageError);
                                                                                                                                                if (recovered) {
                                                                                                                                                    try {
                                                                                                                                                        await chrome.storage.local.set({ [tempFileKey]: originalFormDataFiles });
                                                                                                                                                        console.log('Temp files stored successfully after cleanup');
                                                                                                                                                    } catch (retryError) {
                                                                                                                                                        console.error('Failed to store temp files even after cleanup:', retryError);
                                                                                                                                                        showNotification('Storage full: Cannot store large files. Please remove some data and try again.', 'error', 8000);
                                                                                                                                                        sendBtn.classList.remove('loading');
                                                                                                                                                        sendBtn.disabled = false;
                                                                                                                                                        return;
                                                                                                                                                    }
                                                                                                                                                } else {
                                                                                                                                                    console.error('Storage cleanup failed, cannot store temp files');
                                                                                                                                                    showNotification('Storage quota exceeded: Cannot upload large files. Please free up space.', 'error', 8000);
                                                                                                                                                    sendBtn.classList.remove('loading');
                                                                                                                                                    sendBtn.disabled = false;
                                                                                                                                                    return;
                                                                                                                                                }
                                                                                                                                            } else {
                                                                                                                                                console.error('Error storing temp files:', storageError);
                                                                                                                                                showNotification('Failed to prepare file upload: ' + storageError.message, 'error', 5000);
                                                                                                                                                sendBtn.classList.remove('loading');
                                                                                                                                                sendBtn.disabled = false;
                                                                                                                                                return;
                                                                                                                                            }
                                                                                                                                        }
                                                                                                                                    }

                                                                                                                                    // Create a completely safe copy that prevents message length errors
                                                                                                                                    const safeRequestData = {
                                                                                                                                        ...request,
                                                                                                                                        formDataBody: request.formDataBody ? request.formDataBody.map((item, index) => {
                                                                                                                                            if (item.type === 'file' && item.value) {
                                                                                                                                                // Always replace file data with safe reference for Chrome messaging
                                                                                                                                                return {
                                                                                                                                                    ...item,
                                                                                                                                                    value: `[FILE_REF_${index}]`, // Safe placeholder
                                                                                                                                                    _isFileRef: true,
                                                                                                                                                    _originalIndex: index
                                                                                                                                                };
                                                                                                                                            }
                                                                                                                                            return item;
                                                                                                                                        }) : null
                                                                                                                                    };

                                                                                                                                    const message = {
                                                                                                                                        action: 'executeRequest',
                                                                                                                                        requestData: safeRequestData,
                                                                                                                                        requestId: requestId,
                                                                                                                                        _hasLargeFiles: originalFormDataFiles.length > 0 // Just a flag, not the data
                                                                                                                                    };

                                                                                                                                    // Enhanced progress indication for large files with percentage tracking
                                                                                                                                    if (hasLargeFiles) {
                                                                                                                                        sendBtn.classList.remove('loading');
                                                                                                                                        sendBtn.classList.add('sending');
                                                                                                                                        const btnText = sendBtn.querySelector('.btn-text');
                                                                                                                                        btnText.innerHTML = 'Sending... <span class="progress-percentage">0%</span>';

                                                                                                                                        // Save upload state for persistence
                                                                                                                                        state.activeUpload = {
                                                                                                                                            requestId: requestId, // Now using the pre-declared requestId
                                                                                                                                            isUploading: true,
                                                                                                                                            startTime: Date.now(),
                                                                                                                                            hasLargeFiles: true
                                                                                                                                        };
                                                                                                                                        await saveUploadState();
                                                                                                                                    }

                                                                                                                                    // Start progress polling for large files
                                                                                                                                    let progressInterval;
                                                                                                                                    if (hasLargeFiles) {
                                                                                                                                        progressInterval = setInterval(() => {
                                                                                                                                            chrome.runtime.sendMessage({
                                                                                                                                                action: 'getProgress',
                                                                                                                                                requestId: message.requestId
                                                                                                                                            }, (progressResponse) => {
                                                                                                                                                if (progressResponse && progressResponse.progress) {
                                                                                                                                                    const progress = progressResponse.progress;
                                                                                                                                                    const btnText = sendBtn.querySelector('.btn-text');
                                                                                                                                                    const progressSpan = btnText.querySelector('.progress-percentage');

                                                                                                                                                    if (progressSpan && progress.progress !== undefined) {
                                                                                                                                                        progressSpan.textContent = `${progress.progress}%`;

                                                                                                                                                        // Update button style based on progress stage
                                                                                                                                                        if (progress.stage === 'complete') {
                                                                                                                                                            clearInterval(progressInterval);
                                                                                                                                                            progressSpan.textContent = '100%';
                                                                                                                                                        } else if (progress.stage === 'error') {
                                                                                                                                                            clearInterval(progressInterval);
                                                                                                                                                            progressSpan.textContent = 'Error';
                                                                                                                                                        }
                                                                                                                                                    }
                                                                                                                                                }
                                                                                                                                            }, 500); // Poll every 500ms
                                                                                                                                        }


                chrome.runtime.sendMessage(message, async (response) => {
                                                                                                                                            // Clear progress polling
                                                                                                                                            if (progressInterval) {
                                                                                                                                                clearInterval(progressInterval);
                                                                                                                                            }

                                                                                                                                            // Clear upload state since request completed
                                                                                                                                            await clearUploadState();

                                                                                                                                            sendBtn.classList.remove('loading', 'sending');
                                                                                                                                            const btnText = sendBtn.querySelector('.btn-text');

                                                                                                                                            // Always show the response viewer
                                                                                                                                            responseViewerEl.classList.remove('hidden');
                                                                                                                                            responseStatusEl.textContent = `Status: ${response.status || 'N/A'}`;
                                                                                                                                            responseStatusEl.className = response.isSuccess ? 'status-success' : 'status-error';

                                                                                                                                            // Better response formatting
                                                                                                                                            let responseText = response.body || response.error || 'No response body.';

                                                                                                                                            // Try to format JSON responses for better readability
                                                                                                                                            if (response.isSuccess && response.body) {
                                                                                                                                                try {
                                                                                                                                                    const jsonResponse = JSON.parse(response.body);
                                                                                                                                                    responseText = JSON.stringify(jsonResponse, null, 2);
                                                                                                                                                } catch (e) {
                                                                                                                                                    // Not JSON, use as-is
                                                                                                                                                }
                                                                                                                                            }

                                                                                                                                            // Enhanced message for large file uploads
                                                                                                                                            if (response.isSuccess && response.bodyType === 'form-data') {
                                                                                                                                                let successMessage = "Form-Data request sent successfully. Check your webhook destination to confirm the file was received.";

                                                                                                                                                if (response.hasLargeFile) {
                                                                                                                                                    successMessage += "\n\n📁 Large file successfully transmitted!";
                                                                                                                                                    if (response.requestSize) {
                                                                                                                                                        const sizeInMB = (response.requestSize / (1024 * 1024)).toFixed(1);
                                                                                                                                                        successMessage += ` (${sizeInMB} MB)`;
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                responseBodyEl.textContent = successMessage + "\n\nResponse: " + responseText;
                                                                                                                                            } else {
                                                                                                                                                responseBodyEl.textContent = responseText;
                                                                                                                                            }


                                                                                                                                            if (response.error || !response.isSuccess) {
                                                                                                                                                btnText.textContent = '✖';
                                                                                                                                                sendBtn.style.backgroundColor = 'var(--error-color)';

                                                                                                                                                let errorMessage = response.error || 'Request failed. See response for details.';

                                                                                                                                                // Enhanced error messages for large files
                                                                                                                                                if (response.hasLargeFile) {
                                                                                                                                                    if (response.status === 413) {
                                                                                                                                                        errorMessage = '📁 File too large for this webhook endpoint. Try a smaller file or different endpoint.';
                                                                                                                                                    } else if (response.status === 504 || response.status === 408) {
                                                                                                                                                        errorMessage = '⏱️ Large file upload timed out. The file may be too large or connection too slow.';
                                                                                                                                                    } else if (response.error && response.error.includes('network')) {
                                                                                                                                                        errorMessage = '🌐 Network error during large file upload. Check your connection and try again.';
                                                                                                                                                    }
                                                                                                                                                }

                                                                                                                                                showNotification(errorMessage, 'error');
                                                                                                                                            } else {
                                                                                                                                                btnText.textContent = '✔';
                                                                                                                                                sendBtn.style.backgroundColor = 'var(--success-color)';

                                                                                                                                                let successMessage = 'Request sent successfully!';
                                                                                                                                                if (response.hasLargeFile) {
                                                                                                                                                    successMessage = '🚀 Large file sent successfully!';
                                                                                                                                                }

                                                                                                                                                showNotification(successMessage, 'success');

                                                                                                                                                // The background script now handles all state updates.
                                                                                                                                                // We can update our local state directly from the response to avoid race conditions.
                                                                                                                                                if (response.updatedRequest) {
                                                                                                                                                    const requestIndex = state.requests.findIndex(r => r.id === response.updatedRequest.id);
                                                                                                                                                    if (requestIndex > -1) {
                                                                                                                                                        // Replace the old request object with the updated one from the background script
                                                                                                                                                        state.requests[requestIndex] = response.updatedRequest;
                                                                                                                                                        // Update the use count and sort order in the UI
                                                                                                                                                        state.requests.sort((a, b) => (b.useCount || 0) - (a.useCount || 0));
                                                                                                                                                        render(); // Re-render with the updated state
                                                                                                                                                    } else {
                                                                                                                                                        loadInitialState(); // Fallback
                                                                                                                                                    }
                                                                                                                                                } else {
                                                                                                                                                    loadInitialState(); // Fallback
                                                                                                                                                }
                                                                                                                                            }

                                                                                                                                            setTimeout(() => {
                                                                                                                                                sendBtn.disabled = false;
                                                                                                                                                sendBtn.classList.remove('sending', 'progress');
                                                                                                                                                btnText.textContent = 'Send';
                                                                                                                                                btnText.innerHTML = 'Send'; // Clear any HTML content
                                                                                                                                                sendBtn.style.backgroundColor = 'var(--primary-color)';
                                                                                                                                                sendBtn.style.removeProperty('--progress-width');
                                                                                                                                            }, 2000);
                                                                                                                                        };

                                                                                                                                        testUrlBtn.onclick = () => {
                                                                                                                                            const url = urlInput.value;
                                                                                                                                            if (!url || !/^(https?|file):\/\/[^\s$.?#].[^\s]*$/i.test(url)) {
                                                                                                                                                showNotification('A valid URL is required to run a test.', 'error');
                                                                                                                                                urlInput.classList.add('invalid-field');
                                                                                                                                                urlInput.focus();
                                                                                                                                                return;
                                                                                                                                            }

                                                                                                                                            testUrlBtn.textContent = '...';
                                                                                                                                            testUrlBtn.disabled = true;

                                                                                                                                            chrome.runtime.sendMessage({ action: 'testUrl', url: url }, (response) => {
                                                                                                                                                if (response.error) {
                                                                                                                                                    showNotification(`Test failed: ${response.error}`, 'error');
                                                                                                                                                } else {
                                                                                                                                                    const message = `Test OK: ${response.status} ${response.statusText}`;
                                                                                                                                                    const type = response.ok ? 'success' : 'error';
                                                                                                                                                    showNotification(message, type);
                                                                                                                                                }
                                                                                                                                                testUrlBtn.textContent = 'Test';
                                                                                                                                                testUrlBtn.disabled = false;
                                                                                                                                            };

                                                                                                                                            // --- Import/Export ---
                                                                                                                                            function getFormattedDate() {
                                                                                                                                                const date = new Date();
                                                                                                                                                const day = String(date.getDate()).padStart(2, '0');
                                                                                                                                                const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
                                                                                                                                                const year = date.getFullYear();
                                                                                                                                                return `${day}-${month}-${year}`;
                                                                                                                                            }

                                                                                                                                            exportBtn.onclick = () => {
                                                                                                                                                if (state.requests.length === 0) {
                                                                                                                                                    showNotification('Nothing to export.', 'error');
                                                                                                                                                    return;
                                                                                                                                                }
                                                                                                                                                const dataStr = JSON.stringify(state.requests, null, 2);
                                                                                                                                                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                                                                                                                                                const url = URL.createObjectURL(dataBlob);
                                                                                                                                                const a = document.createElement('a');
                                                                                                                                                a.href = url;
                                                                                                                                                a.download = `requests-export-${getFormattedDate()}.json`;
                                                                                                                                                a.click();
                                                                                                                                                URL.revokeObjectURL(url);
                                                                                                                                                showNotification('Requests exported!', 'success');
                                                                                                                                            };

                                                                                                                                            importFileEl.onchange = (e) => {
                                                                                                                                                const file = e.target.files[0];
                                                                                                                                                if (!file) return;

                                                                                                                                                const reader = new FileReader();
                                                                                                                                                reader.onload = (event) => {
                                                                                                                                                    try {
                                                                                                                                                        const importedRequests = JSON.parse(event.target.result);
                                                                                                                                                        if (!Array.isArray(importedRequests)) {
                                                                                                                                                            throw new Error('Imported file is not a valid array.');
                                                                                                                                                        }

                                                                                                                                                        // Rigorous validation of imported data
                                                                                                                                                        const isValid = importedRequests.every(req =>
                                                                                                                                                            typeof req === 'object' &&
                                                                                                                                                            req !== null &&
                                                                                                                                                            typeof req.id === 'string' &&
                                                                                                                                                            typeof req.name === 'string' &&
                                                                                                                                                            typeof req.isAdvanced === 'boolean' &&
                                                                                                                                                            typeof req.method === 'string' &&
                                                                                                                                                            typeof req.url === 'string' &&
                                                                                                                                                            Array.isArray(req.headers) &&
                                                                                                                                                            req.headers.every(h => typeof h.key === 'string' && typeof h.value === 'string') &&
                                                                                                                                                            Array.isArray(req.simpleBody) &&
                                                                                                                                                            req.simpleBody.every(b => typeof b.key === 'string' && typeof b.value === 'string') &&
                                                                                                                                                            typeof req.body === 'string' &&
                                                                                                                                                            typeof req.createdAt === 'number' &&
                                                                                                                                                            typeof req.useCount === 'number' &&
                                                                                                                                                            typeof req.lastUsed === 'number'
                                                                                                                                                        );

                                                                                                                                                        if (!isValid) {
                                                                                                                                                            throw new Error('Imported data has an invalid structure or missing fields.');
                                                                                                                                                        }

                                                                                                                                                        const importActions = [
                                                                                                                                                            {
                                                                                                                                                                text: 'Confirm',
                                                                                                                                                                onClick: () => {
                                                                                                                                                                    state.requests = importedRequests;
                                                                                                                                                                    state.selectedRequestId = null; // Reset selection
                                                                                                                                                                    saveState(false);
                                                                                                                                                                    // Select the first imported request by default
                                                                                                                                                                    if (state.requests.length > 0) {
                                                                                                                                                                        selectRequest(state.requests[0].id);
                                                                                                                                                                    } else {
                                                                                                                                                                        render();
                                                                                                                                                                    }
                                                                                                                                                                    showNotification('Requests imported successfully!', 'success');
                                                                                                                                                                }
                                                                                                                                                            },
                                                                                                                                                            {
                                                                                                                                                                text: 'Cancel',
                                                                                                                                                                onClick: () => { /* Do nothing */ }
                                                                                                                                                            }
                                                                                                                                                        ];
                                                                                                                                                        showNotification('This will replace all your current requests. Are you sure?', 'confirm', null, importActions);
                                                                                                                                                    } catch (err) {
                                                                                                                                                        showNotification(`Import failed: ${err.message}`, 'error');
                                                                                                                                                    } finally {
                                                                                                                                                        // Reset the file input so the same file can be loaded again
                                                                                                                                                        importFileEl.value = '';
                                                                                                                                                    }
                                                                                                                                                };
                                                                                                                                                reader.readAsText(file);
                                                                                                                                            };

                                                                                                                                            // --- Resizable Panels ---
                                                                                                                                            /**
                                                                                                                                             * Sets up resizable panel functionality
                                                                                                                                             * @function setupResizablePanels
                                                                                                                                             */
                                                                                                                                            function setupResizablePanels() {
                                                                                                                                                const resizeHandle = document.getElementById('resize-handle');
                                                                                                                                                const panelLeft = document.getElementById('panel-left');
                                                                                                                                                const panelRight = document.getElementById('panel-right');
                                                                                                                                                const container = document.querySelector('.container');

                                                                                                                                                let isResizing = false;
                                                                                                                                                let startX = 0;
                                                                                                                                                let startLeftWidth = 0;

                                                                                                                                                if (resizeHandle && panelLeft && panelRight && container) {
                                                                                                                                                    // Load saved panel width from storage
                                                                                                                                                    chrome.storage.local.get({ panelLeftWidth: 220 }, (data) => {
                                                                                                                                                        panelLeft.style.width = `${data.panelLeftWidth}px`;

                                                                                                                                                        resizeHandle.addEventListener('mousedown', (e) => {
                                                                                                                                                            isResizing = true;
                                                                                                                                                            startX = e.clientX;
                                                                                                                                                            startLeftWidth = parseInt(window.getComputedStyle(panelLeft).width, 10);

                                                                                                                                                            // Add visual feedback
                                                                                                                                                            document.body.style.cursor = 'col-resize';
                                                                                                                                                            resizeHandle.style.backgroundColor = 'var(--primary-color)';

                                                                                                                                                            // Prevent text selection during resize
                                                                                                                                                            document.body.style.userSelect = 'none';

                                                                                                                                                            e.preventDefault();

                                                                                                                                                            document.addEventListener('mousemove', (e) => {
                                                                                                                                                                if (!isResizing) return;

                                                                                                                                                                const deltaX = e.clientX - startX;
                                                                                                                                                                const newLeftWidth = startLeftWidth + deltaX;

                                                                                                                                                                // Constrain the width between min and max values
                                                                                                                                                                const minWidth = 180;
                                                                                                                                                                const maxWidth = 400;
                                                                                                                                                                const containerWidth = container.offsetWidth;
                                                                                                                                                                const maxAllowedWidth = Math.min(maxWidth, containerWidth - 300); // Leave at least 300px for right panel

                                                                                                                                                                const clampedWidth = Math.max(minWidth, Math.min(newLeftWidth, maxAllowedWidth));

                                                                                                                                                                panelLeft.style.width = `${clampedWidth}px`;

                                                                                                                                                                document.addEventListener('mouseup', () => {
                                                                                                                                                                    if (isResizing) {
                                                                                                                                                                        isResizing = false;

                                                                                                                                                                        // Remove visual feedback
                                                                                                                                                                        document.body.style.cursor = '';
                                                                                                                                                                        resizeHandle.style.backgroundColor = '';
                                                                                                                                                                        document.body.style.userSelect = '';

                                                                                                                                                                        // Save the new width to storage
                                                                                                                                                                        const currentWidth = parseInt(panelLeft.style.width, 10);
                                                                                                                                                                        chrome.storage.local.set({ panelLeftWidth: currentWidth });

                                                                                                                                                                        showNotification(`Panel width: ${currentWidth}px`, 'success', 1500);
                                                                                                                                                                    }

                                                                                                                                                                    // Handle double-click to reset to default width
                                                                                                                                                                    resizeHandle.addEventListener('dblclick', () => {
                                                                                                                                                                        const defaultWidth = 220;
                                                                                                                                                                        panelLeft.style.width = `${defaultWidth}px`;
                                                                                                                                                                        chrome.storage.local.set({ panelLeftWidth: defaultWidth });
                                                                                                                                                                        showNotification('Panel width reset to default', 'success', 1500);
                                                                                                                                                                    }
            }

            // --- Full-Screen Mode ---
            /**
             * Sets up full-screen mode functionality
             * @function setupFullScreenMode
             */
            function setupFullScreenMode() {
                                                                                                                                                                        const fullScreenBtn = document.getElementById('fullscreen-btn');
                                                                                                                                                                        const container = document.querySelector('.container');

                                                                                                                                                                        if (fullScreenBtn && container) {
                                                                                                                                                                            // Load saved full-screen mode state
                                                                                                                                                                            chrome.storage.local.get({ fullScreenMode: false }, (data) => {
                                                                                                                                                                                if (data.fullScreenMode) {
                                                                                                                                                                                    container.classList.add('fullscreen-mode');
                                                                                                                                                                                    updateFullScreenIcon(true);
                                                                                                                                                                                }

                                                                                                                                                                                fullScreenBtn.addEventListener('click', () => {
                                                                                                                                                                                    const isFullScreen = container.classList.contains('fullscreen-mode');

                                                                                                                                                                                    if (isFullScreen) {
                                                                                                                                                                                        // Exit full-screen mode
                                                                                                                                                                                        container.classList.remove('fullscreen-mode');
                                                                                                                                                                                        chrome.storage.local.set({ fullScreenMode: false });
                                                                                                                                                                                        updateFullScreenIcon(false);
                                                                                                                                                                                        showNotification('Full-screen mode disabled', 'success', 1500);
                                                                                                                                                                                    } else {
                                                                                                                                                                                        // Enter full-screen mode
                                                                                                                                                                                        container.classList.add('fullscreen-mode');
                                                                                                                                                                                        chrome.storage.local.set({ fullScreenMode: true });
                                                                                                                                                                                        updateFullScreenIcon(true);
                                                                                                                                                                                        showNotification('Full-screen mode enabled - Press F11 for browser full-screen', 'info', 3000);
                                                                                                                                                                                    }
                                                                                                                                                                                }
            }

            /**
             * Updates the full-screen mode button icon based on current state
             * @function updateFullScreenIcon
             * @param {boolean} isFullScreen - Whether full-screen mode is enabled
             */
            function updateFullScreenIcon(isFullScreen) {
                                                                                                                                                                                    const fullScreenBtn = document.getElementById('fullscreen-btn');
                                                                                                                                                                                    if (fullScreenBtn) {
                                                                                                                                                                                        const icon = fullScreenBtn.querySelector('svg path');
                                                                                                                                                                                        if (isFullScreen) {
                                                                                                                                                                                            // Exit full-screen icon
                                                                                                                                                                                            icon.setAttribute('d', 'M14,14H19V16H16V19H14V14M5,14H10V19H8V16H5V14M8,5H10V10H5V8H8V5M19,8V10H14V5H16V8H19Z');
                                                                                                                                                                                            fullScreenBtn.title = 'Exit Full-Screen Mode';
                                                                                                                                                                                        } else {
                                                                                                                                                                                            // Enter full-screen icon
                                                                                                                                                                                            icon.setAttribute('d', 'M5,5H10V7H7V10H5V5M14,5H19V10H17V7H14V5M17,14H19V19H14V17H17V14M10,17V19H5V14H7V17H10Z');
                                                                                                                                                                                            fullScreenBtn.title = 'Enter Full-Screen Mode';
                                                                                                                                                                                        }
                                                                                                                                                                                    }
                                                                                                                                                                                }

            // --- Keyboard Shortcuts ---
            function setupKeyboardShortcuts() {
                                                                                                                                                                                    document.addEventListener('keydown', (e) => {
                                                                                                                                                                                        // Ctrl/Cmd + Enter: Send request
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            if (!sendBtn.disabled) {
                                                                                                                                                                                                sendBtn.click();
                                                                                                                                                                                            }
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Ctrl/Cmd + N: New request
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            addNewRequestBtn.click();
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Ctrl/Cmd + S: Save request (focus out to trigger save)
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            if (document.activeElement) {
                                                                                                                                                                                                document.activeElement.blur();
                                                                                                                                                                                            }
                                                                                                                                                                                            showNotification('Request saved', 'success', 1000);
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Ctrl/Cmd + T: Toggle theme
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            toggleTheme();
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Ctrl/Cmd + F: Focus search
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            searchInputEl.focus();
                                                                                                                                                                                            searchInputEl.select();
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Ctrl/Cmd + Shift + F: Toggle full-screen mode
                                                                                                                                                                                        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'F') {
                                                                                                                                                                                            e.preventDefault();
                                                                                                                                                                                            const fullScreenBtn = document.getElementById('fullscreen-btn');
                                                                                                                                                                                            if (fullScreenBtn) {
                                                                                                                                                                                                fullScreenBtn.click();
                                                                                                                                                                                            }
                                                                                                                                                                                            return;
                                                                                                                                                                                        }

                                                                                                                                                                                        // Escape: Clear search or close notifications
                                                                                                                                                                                        if (e.key === 'Escape') {
                                                                                                                                                                                            if (searchInputEl.value) {
                                                                                                                                                                                                searchInputEl.value = '';
                                                                                                                                                                                                state.searchQuery = '';
                                                                                                                                                                                                render();
                                                                                                                                                                                            } else if (!notificationBarEl.classList.contains('hidden')) {
                                                                                                                                                                                                notificationBarEl.classList.add('hidden');
                                                                                                                                                                                            }
                                                                                                                                                                                            return;
                                                                                                                                                                                        }
                                                                                                                                                                                    }


            // Storage cleanup button event listener
            if (storageCleanupBtn) {
                                                                                                                                                                                        storageCleanupBtn.onclick = () => {
                                                                                                                                                                                            handleManualStorageCleanup();
                                                                                                                                                                                        };
                                                                                                                                                                                    }

                                                                                                                                                                                    // --- Initialization ---
                                                                                                                                                                                    // --- Initialization ---
                                                                                                                                                                                    initializeTheme();
                                                                                                                                                                                    loadInitialState();
                                                                                                                                                                                    setupResizablePanels();
                                                                                                                                                                                    setupFullScreenMode();
                                                                                                                                                                                    setupKeyboardShortcuts();
                                                                                                                                                                                });
