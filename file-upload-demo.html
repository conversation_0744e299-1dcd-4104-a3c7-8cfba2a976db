<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced File Upload Demo - Multi Webhook Sender</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            height: auto;
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--panel-background);
        }

        .demo-section h2 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .demo-section p {
            color: var(--light-text-color);
            line-height: 1.5;
        }

        .form-data-item {
            margin-bottom: 20px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
            font-size: 16px;
        }

        .video-types {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .video-type {
            background-color: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <h1>🎥 Enhanced File Upload Demo</h1>
    <p>The Multi Webhook Sender now supports advanced file upload capabilities with excellent video file handling!</p>

    <div class="demo-section">
        <h2>📁 File Upload Area - Before Selection</h2>
        <p>This is how the file upload area appears before any file is selected. It supports drag & drop!</p>

        <div class="form-data-item">
            <div class="form-data-controls">
                <select class="form-data-type">
                    <option value="file" selected>File</option>
                </select>
                <textarea class="form-data-key" placeholder="Key" rows="1">video_file</textarea>
            </div>
            <div class="form-data-value-wrapper">
                <div class="file-upload-area">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <span class="upload-main">Choose file or drag & drop</span>
                            <span class="upload-hint">Supports videos, images, documents, and more</span>
                        </div>
                    </div>
                    <input type="file" class="form-data-file" accept="*/*" style="pointer-events: none;">
                    <div class="upload-progress hidden">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <span class="progress-text">Uploading...</span>
                    </div>
                </div>
            </div>
            <button class="icon-btn remove-form-data-row-btn">-</button>
        </div>
    </div>

    <div class="demo-section">
        <h2>🎬 Video File Display</h2>
        <p>When a video file is uploaded, it shows enhanced information with video-specific styling:</p>

        <div class="form-data-item">
            <div class="form-data-controls">
                <select class="form-data-type">
                    <option value="file" selected>File</option>
                </select>
                <textarea class="form-data-key" placeholder="Key" rows="1">video_file</textarea>
            </div>
            <div class="form-data-value-wrapper">
                <div class="form-data-file-display">
                    <div class="file-info">
                        <div class="file-icon video-file">
                            🎥
                        </div>
                        <div class="file-details">
                            <div class="form-data-filename" title="my_awesome_video.mp4">
                                <span class="filename-start">my_awesome_video</span><span
                                    class="filename-ext">.mp4</span>
                            </div>
                            <div class="file-size">25.6 MB</div>
                            <div class="file-type-label">📹 Video File</div>
                        </div>
                    </div>
                    <button class="icon-btn clear-file-btn" title="Clear file">✕</button>
                </div>
            </div>
            <button class="icon-btn remove-form-data-row-btn">-</button>
        </div>
    </div>

    <div class="demo-section">
        <h2>🖼️ Image File Display</h2>
        <p>Image files get their own distinct styling and icons:</p>

        <div class="form-data-item">
            <div class="form-data-controls">
                <select class="form-data-type">
                    <option value="file" selected>File</option>
                </select>
                <textarea class="form-data-key" placeholder="Key" rows="1">image_file</textarea>
            </div>
            <div class="form-data-value-wrapper">
                <div class="form-data-file-display">
                    <div class="file-info">
                        <div class="file-icon image-file">
                            🖼️
                        </div>
                        <div class="file-details">
                            <div class="form-data-filename" title="screenshot.png">
                                <span class="filename-start">screenshot</span><span class="filename-ext">.png</span>
                            </div>
                            <div class="file-size">2.3 MB</div>
                            <div class="file-type-label">🖼️ Image File</div>
                        </div>
                    </div>
                    <button class="icon-btn clear-file-btn" title="Clear file">✕</button>
                </div>
            </div>
            <button class="icon-btn remove-form-data-row-btn">-</button>
        </div>
    </div>

    <div class="demo-section">
        <h2>📄 Document File Display</h2>
        <p>Other file types also get appropriate icons and styling:</p>

        <div class="form-data-item">
            <div class="form-data-controls">
                <select class="form-data-type">
                    <option value="file" selected>File</option>
                </select>
                <textarea class="form-data-key" placeholder="Key" rows="1">document</textarea>
            </div>
            <div class="form-data-value-wrapper">
                <div class="form-data-file-display">
                    <div class="file-info">
                        <div class="file-icon">
                            📄
                        </div>
                        <div class="file-details">
                            <div class="form-data-filename" title="important_document.pdf">
                                <span class="filename-start">important_document</span><span
                                    class="filename-ext">.pdf</span>
                            </div>
                            <div class="file-size">1.2 MB</div>
                        </div>
                    </div>
                    <button class="icon-btn clear-file-btn" title="Clear file">✕</button>
                </div>
            </div>
            <button class="icon-btn remove-form-data-row-btn">-</button>
        </div>
    </div>

    <div class="demo-section">
        <h2>🚀 Key Features</h2>
        <ul class="feature-list">
            <li>🎥 <strong>Excellent Video Support</strong> - MP4, AVI, MKV, MOV, WebM, and more</li>
            <li>📱 <strong>Drag & Drop Upload</strong> - Simply drag files onto the upload area</li>
            <li>📊 <strong>Upload Progress Bar</strong> - Real-time progress feedback during file upload</li>
            <li>📏 <strong>File Size Display</strong> - Shows file size in human-readable format</li>
            <li>🎯 <strong>File Type Detection</strong> - Automatic icon and label based on file type</li>
            <li>🎨 <strong>Visual Feedback</strong> - Different colors and icons for different file types</li>
            <li>🧹 <strong>Easy File Management</strong> - Clear files with one click</li>
            <li>⚡ <strong>Large File Handling</strong> - Optimized for handling large video files</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>🎬 Supported Video Formats</h2>
        <p>The extension automatically detects and provides enhanced handling for these video formats:</p>
        <div class="video-types">
            <span class="video-type">.mp4</span>
            <span class="video-type">.avi</span>
            <span class="video-type">.mkv</span>
            <span class="video-type">.mov</span>
            <span class="video-type">.wmv</span>
            <span class="video-type">.flv</span>
            <span class="video-type">.webm</span>
            <span class="video-type">.m4v</span>
            <span class="video-type">.3gp</span>
            <span class="video-type">.ogv</span>
        </div>
    </div>

    <div class="demo-section">
        <h2>💡 Usage Tips</h2>
        <ul class="feature-list">
            <li>🖱️ <strong>Click to Browse</strong> - Click anywhere in the upload area to open file browser</li>
            <li>🖐️ <strong>Drag & Drop</strong> - Drag files directly from your file manager</li>
            <li>🎯 <strong>File Type Feedback</strong> - The interface shows specific icons for videos, images,
                documents</li>
            <li>📊 <strong>Progress Tracking</strong> - Watch the progress bar for upload status</li>
            <li>🧹 <strong>Quick Clear</strong> - Use the ✕ button to quickly remove uploaded files</li>
            <li>🔄 <strong>Re-upload</strong> - Clearing a file returns you to the upload area</li>
        </ul>
    </div>

    <script>
        // Demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Simulate drag over effect for demo
            const uploadAreas = document.querySelectorAll('.file-upload-area');

            uploadAreas.forEach(area => {
                area.addEventListener('mouseenter', () => {
                    area.style.borderColor = 'var(--primary-color)';
                    area.style.backgroundColor = 'rgba(74, 144, 226, 0.05)';
                });

                area.addEventListener('mouseleave', () => {
                    area.style.borderColor = 'var(--border-color)';
                    area.style.backgroundColor = 'var(--panel-background)';
                });
            });

            // Demo clear button functionality
            const clearBtns = document.querySelectorAll('.clear-file-btn');
            clearBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    alert('In the real extension, this would clear the file and return to upload area!');
                });
            });
        });
    </script>
</body>

</html>