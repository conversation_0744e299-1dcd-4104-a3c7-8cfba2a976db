// Final fix script to close the main DOMContentLoaded event listener
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'popup.js');

try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // The file should end with:
    // setupKeyboardShortcuts();
    // });

    // But it's currently ending with:
    // setupKeyboardShortcuts();
    // });
    // (missing the final closing bracket for DOMContentLoaded)

    const lines = content.split('\n');
    const lastLine = lines[lines.length - 1].trim();

    console.log(`Last line of file: "${lastLine}"`);

    // Check if the file ends with just }); (which would close DOMContentLoaded)
    if (lastLine === '});') {
        console.log('✅ File already properly closed!');

        // But let's verify the bracket structure by checking syntax
        try {
            new Function(content);
            console.log('✅ JavaScript syntax is valid!');
        } catch (syntaxError) {
            console.log('❌ Syntax error found:', syntaxError.message);
            console.log('Attempting to fix...');

            // Add the missing closing bracket
            if (!content.endsWith('\n')) {
                content += '\n';
            }
            content += '});\n';

            fs.writeFileSync(filePath, content, 'utf8');
            console.log('✅ Added missing closing bracket for DOMContentLoaded event listener!');
        }
    } else {
        console.log('❌ File does not end with });');
        console.log('Adding the missing closing bracket...');

        // Ensure proper line ending
        if (!content.endsWith('\n')) {
            content += '\n';
        }

        // Add the missing closing bracket
        content += '});\n';

        fs.writeFileSync(filePath, content, 'utf8');
        console.log('✅ Added missing closing bracket for DOMContentLoaded event listener!');
    }

    console.log('🎉 Fix completed! Theme switching and body type switching should now work correctly.');

} catch (error) {
    console.error('❌ Error fixing popup.js:', error.message);
}