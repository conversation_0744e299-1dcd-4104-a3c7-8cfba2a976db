// Test script to validate JavaScript syntax
const fs = require('fs');

try {
    const content = fs.readFileSync('popup.js', 'utf8');

    // Try to parse the JavaScript
    new Function(content);

    console.log('✅ JavaScript syntax is VALID!');
    console.log('🎉 Theme switching and body type switching should now work correctly.');
    console.log('');
    console.log('📝 Summary of fixes applied:');
    console.log('   • Added missing closing bracket to setupKeyboardShortcuts() function');
    console.log('   • Removed extra bracket that was incorrectly added');
    console.log('   • Proper function closure restored');
    console.log('');
    console.log('🔧 You can now test:');
    console.log('   • Dark/Light theme switching');
    console.log('   • Body type switching (JSON, Raw, Form-Data)');
    console.log('   • All other JavaScript functionality');

} catch (error) {
    console.error('❌ JavaScript syntax error found:');
    console.error(error.message);

    if (error.message.includes('Unexpected token')) {
        console.log('');
        console.log('💡 This suggests there may still be bracket or syntax issues.');
    }
}