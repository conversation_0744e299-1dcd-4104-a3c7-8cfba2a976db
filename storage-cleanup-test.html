<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Storage Cleanup Test - Multi Webhook Sender</title>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-btn {
            padding: 8px 12px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .test-btn.create {
            background: #3498db;
            color: white;
        }

        .test-btn.cleanup {
            background: #2ecc71;
            color: white;
        }

        .test-btn.emergency {
            background: #e74c3c;
            color: white;
        }

        .results {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }

        .success {
            color: #27ae60;
            font-weight: bold;
        }

        .error {
            color: #e74c3c;
            font-weight: bold;
        }

        .warning {
            color: #f39c12;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <h1>🧪 Storage Cleanup Test Suite</h1>
    <p>Test all storage cleanup functionality for the Multi Webhook Sender extension.</p>

    <div class="test-section">
        <h3>📊 Test Data Management</h3>
        <button class="test-btn create" onclick="createTestData()">Create Test Files</button>
        <button class="test-btn cleanup" onclick="analyzeStorage()">Analyze Storage</button>
        <button class="test-btn emergency" onclick="resetStorage()">Reset Storage</button>
        <div id="data-results" class="results">Ready to create test data...</div>
    </div>

    <div class="test-section">
        <h3>🧹 Cleanup Functions</h3>
        <button class="test-btn cleanup" onclick="testOrphanedCleanup()">Test Orphaned Cleanup</button>
        <button class="test-btn emergency" onclick="testEmergencyCleanup()">Test Emergency Cleanup</button>
        <button class="test-btn cleanup" onclick="testManualCleanup()">Test Manual Cleanup</button>
        <div id="cleanup-results" class="results">Ready to test cleanup functions...</div>
    </div>

    <div class="test-section">
        <h3>🚀 Full Test Suite</h3>
        <button class="test-btn" onclick="runFullTest()">Run Complete Test</button>
        <div id="full-results" class="results">Ready to run comprehensive test...</div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        async function createTestData() {
            document.getElementById('data-results').innerHTML = '';
            try {
                const currentTime = Date.now();
                const oldTime = currentTime - (30 * 60 * 1000); // 30 minutes ago

                // Create recent temp files
                for (let i = 0; i < 3; i++) {
                    const key = `temp_files_${currentTime + i * 1000}`;
                    const data = Array(50).fill().map((_, idx) => ({ name: `test_${i}_${idx}.txt`, size: 1000 }));
                    await chrome.storage.local.set({ [key]: data });
                }

                // Create old temp files
                for (let i = 0; i < 2; i++) {
                    const key = `temp_files_${oldTime + i * 1000}`;
                    const data = Array(30).fill().map((_, idx) => ({ name: `old_${i}_${idx}.txt`, size: 500 }));
                    await chrome.storage.local.set({ [key]: data });
                }

                log('data-results', '✅ Created 5 test temporary files (3 recent, 2 old)', 'success');
                await analyzeStorage();

            } catch (error) {
                log('data-results', `❌ Error creating test data: ${error.message}`, 'error');
            }
        }

        async function analyzeStorage() {
            try {
                const allItems = await chrome.storage.local.get(null);
                const tempFiles = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                const usage = await chrome.storage.local.getBytesInUse();
                const maxBytes = chrome.storage.local.QUOTA_BYTES || 10 * 1024 * 1024;

                log('data-results', `📊 Storage Analysis:`, 'info');
                log('data-results', `  • Total items: ${Object.keys(allItems).length}`, 'info');
                log('data-results', `  • Temp files: ${tempFiles.length}`, 'info');
                log('data-results', `  • Usage: ${formatBytes(usage)} / ${formatBytes(maxBytes)} (${((usage / maxBytes) * 100).toFixed(1)}%)`, 'info');

            } catch (error) {
                log('data-results', `❌ Error analyzing storage: ${error.message}`, 'error');
            }
        }

        async function resetStorage() {
            try {
                const allItems = await chrome.storage.local.get(null);
                const tempFiles = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));

                for (const key of tempFiles) {
                    await chrome.storage.local.remove(key);
                }

                log('data-results', `✅ Reset complete: Removed ${tempFiles.length} temp files`, 'success');

            } catch (error) {
                log('data-results', `❌ Error resetting storage: ${error.message}`, 'error');
            }
        }

        async function testOrphanedCleanup() {
            document.getElementById('cleanup-results').innerHTML = '';
            try {
                log('cleanup-results', '🧹 Testing orphaned file cleanup...', 'info');

                const allItems = await chrome.storage.local.get(null);
                const tempFiles = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
                const currentTime = Date.now();
                let cleanedCount = 0;

                for (const key of tempFiles) {
                    const timestamp = parseInt(key.replace('temp_files_', ''));
                    if (!isNaN(timestamp)) {
                        const age = currentTime - timestamp;
                        if (age > 5 * 60 * 1000) { // Older than 5 minutes
                            await chrome.storage.local.remove(key);
                            cleanedCount++;
                            log('cleanup-results', `  ✅ Removed old file: ${key}`, 'success');
                        }
                    }
                }

                log('cleanup-results', `✅ Orphaned cleanup completed: ${cleanedCount} files cleaned`, 'success');

            } catch (error) {
                log('cleanup-results', `❌ Error in orphaned cleanup: ${error.message}`, 'error');
            }
        }

        async function testEmergencyCleanup() {
            document.getElementById('cleanup-results').innerHTML = '';
            try {
                if (!confirm('🚨 This will remove ALL temporary files. Continue?')) {
                    log('cleanup-results', '❌ Emergency cleanup cancelled', 'warning');
                    return;
                }

                log('cleanup-results', '🚨 Testing emergency cleanup...', 'warning');

                const allItems = await chrome.storage.local.get(null);
                const tempFiles = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));

                for (const key of tempFiles) {
                    await chrome.storage.local.remove(key);
                }

                await chrome.storage.local.remove('uploadState');

                log('cleanup-results', `🚨 Emergency cleanup completed: ${tempFiles.length} files removed`, 'success');

            } catch (error) {
                log('cleanup-results', `❌ Error in emergency cleanup: ${error.message}`, 'error');
            }
        }

        async function testManualCleanup() {
            document.getElementById('cleanup-results').innerHTML = '';
            try {
                log('cleanup-results', '🖱️ Testing manual cleanup...', 'info');

                const usageBefore = await chrome.storage.local.getBytesInUse();

                // Run emergency cleanup
                const allItems = await chrome.storage.local.get(null);
                const tempFiles = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));

                for (const key of tempFiles) {
                    await chrome.storage.local.remove(key);
                }

                const usageAfter = await chrome.storage.local.getBytesInUse();
                const freed = (usageBefore - usageAfter) / (1024 * 1024);

                log('cleanup-results', `✅ Manual cleanup completed: ${tempFiles.length} files, ${freed.toFixed(2)}MB freed`, 'success');

            } catch (error) {
                log('cleanup-results', `❌ Error in manual cleanup: ${error.message}`, 'error');
            }
        }

        async function runFullTest() {
            document.getElementById('full-results').innerHTML = '';
            try {
                log('full-results', '🚀 Starting comprehensive test suite...', 'info');

                // Create test data
                await createTestData();
                log('full-results', '✅ Phase 1: Test data created', 'success');

                // Test orphaned cleanup
                await testOrphanedCleanup();
                log('full-results', '✅ Phase 2: Orphaned cleanup tested', 'success');

                // Create more data and test emergency cleanup
                await createTestData();
                await testEmergencyCleanup();
                log('full-results', '✅ Phase 3: Emergency cleanup tested', 'success');

                log('full-results', '🎉 ALL TESTS COMPLETED SUCCESSFULLY!', 'success');
                log('full-results', '✅ Storage cleanup functionality is working correctly', 'success');

            } catch (error) {
                log('full-results', `❌ Full test suite failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>

</html>