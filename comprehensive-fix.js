const fs = require('fs');
const path = require('path');

console.log('🔧 Restoring popup.js structure...');

const filePath = path.join(__dirname, 'popup.js');
let content = fs.readFileSync(filePath, 'utf8');

// Fix 1: Restore the loadInitialState function structure
const loadInitialStateFixed = `    async function loadInitialState() {
        try {
            // Clean up orphaned temp files on startup
            await cleanupOrphanedTempFiles(15); // Clean files older than 15 minutes

            const { requests, settings, sortBy, uploadState } = await chrome.storage.local.get({
                requests: [],
                settings: { extremeClean: false, keepData: false },
                sortBy: 'useCount',
                uploadState: null
            });

            state.requests = requests || [];
            state.settings = { ...state.settings, ...settings };
            state.sortBy = sortBy || 'useCount';

            // Restore upload state if exists
            if (uploadState && uploadState.isUploading) {
                state.activeUpload = uploadState;
                console.log('Restored active upload state:', uploadState);
                // Check if upload is still active and resume progress tracking
                await resumeProgressTracking();
            }

            // Set the extreme clean checkbox state
            extremeCleanCheckbox.checked = state.settings.extremeClean;

            // Select the last selected request or the first one
            const lastSelectedId = await getLastSelectedId();
            if (lastSelectedId && state.requests.find(r => r.id === lastSelectedId)) {
                selectRequest(lastSelectedId);
            } else if (state.requests.length > 0) {
                selectRequest(state.requests[0].id);
            }

            render();
        } catch (error) {
            console.error('Failed to load initial state:', error);
            render();
        }
    }`;

// Fix the broken loadInitialState function
content = content.replace(
    /async function loadInitialState\(\) \{[\s\S]*?state\.requests = requests \|\| \[\];[\s\S]*?(?=\s*async function|$)/,
    loadInitialStateFixed + '\n\n'
);

// Add missing closing braces for all functions
const functionsToFix = [
    'saveUploadState',
    'clearUploadState',
    'handleUploadComplete',
    'togglePin',
    'duplicateRequest',
    'selectRequest',
    'render',
    'flattenObject',
    'updateState',
    'checkStorageUsage',
    'cleanupOrphanedTempFiles',
    'emergencyStorageCleanup',
    'handleManualStorageCleanup'
];

// For each function that might be missing closing braces, ensure they are properly closed
functionsToFix.forEach(funcName => {
    // Look for function definitions and ensure they have proper closures
    const funcRegex = new RegExp(`(function ${funcName}\\([^)]*\\)\\s*\\{[\\s\\S]*?)(\\n\\s*(?:function|async function|$))`, 'g');
    content = content.replace(funcRegex, (match, funcBody, nextFunc) => {
        // Count opening and closing braces in the function body
        const openBraces = (funcBody.match(/\{/g) || []).length;
        const closeBraces = (funcBody.match(/\}/g) || []).length;

        if (openBraces > closeBraces) {
            const missing = openBraces - closeBraces;
            console.log(`Adding ${missing} closing brace(s) to ${funcName}`);
            return funcBody + '\n' + '    }'.repeat(missing) + nextFunc;
        }
        return match;
    });
});

// Ensure the file ends with proper DOMContentLoaded closure
if (!content.trim().endsWith('});')) {
    content = content.trim() + '\n});';
}

// Write the fixed content
fs.writeFileSync(filePath, content, 'utf8');
console.log('✅ Restored popup.js structure');
console.log('📝 Check syntax errors with get_problems to see remaining issues');