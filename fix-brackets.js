const fs = require('fs');
const path = require('path');

// Read the file
const filePath = path.join(__dirname, 'popup.js');
let content = fs.readFileSync(filePath, 'utf8');

// Split into lines
let lines = content.split('\n');

// Remove lines that are just "}); " at the end
// Keep the last instance and remove any extra ones
let lastValidIndex = -1;
for (let i = lines.length - 1; i >= 0; i--) {
    const trimmed = lines[i].trim();
    if (trimmed === '});' && lastValidIndex === -1) {
        // This is the valid closing bracket for DOMContentLoaded
        lastValidIndex = i;
    } else if (trimmed === '});' && lastValidIndex !== -1) {
        // This is an extra bracket - remove it
        lines.splice(i, 1);
        console.log(`Removed extra bracket at line ${i + 1}`);
    }
}

// Write back to file
fs.writeFileSync(filePath, lines.join('\n'), 'utf8');
console.log('✅ Fixed popup.js bracket structure');