// Second fix script to remove extra bracket
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'popup.js');

try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // Remove the extra bracket using line-based approach
    const lines = content.split('\n');
    let fixedLines = [];

    for (let i = 0; i < lines.length; i++) {
        // Skip the line that only contains "            }" before the storage cleanup comment
        if (lines[i] === '            }' &&
            i + 1 < lines.length && lines[i + 1] === '' &&
            i + 2 < lines.length && lines[i + 2].includes('// Storage cleanup button event listener')) {
            // This is the extra bracket - skip it
            console.log(`Removing extra bracket on line ${i + 1}`);
            continue;
        }
        fixedLines.push(lines[i]);
    }

    // Write back to file
    fs.writeFileSync(filePath, fixedLines.join('\n'), 'utf8');
    console.log('✅ Successfully removed extra bracket!');
    console.log('🎉 JavaScript syntax should now be completely correct.');

} catch (error) {
    console.error('❌ Error fixing popup.js:', error.message);
}