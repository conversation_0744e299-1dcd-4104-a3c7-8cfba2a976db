const fs = require('fs');
const path = require('path');

// First, let's restore from one of the demo files that likely has the correct structure
const demoFiles = [
    'fixes-complete-demo.html',
    'large-file-fix-demo.html',
    'storage-fix-demo.html'
];

console.log('Attempting to restore popup.js from working structure...');

// For now, let's recreate the proper ending manually
const filePath = path.join(__dirname, 'popup.js');
let content = fs.readFileSync(filePath, 'utf8');

// The file should have a single DOMContentLoaded listener
// Let's fix the specific structural issues

// Replace the broken structure with proper closure
content = content.replace(/async function loadInitialState\(\) \{\s+try \{\s+\/\/ Clean up orphaned temp files on startup[\s\S]*?}\s*state\.requests = requests \|\| \[\];/g,
    `async function loadInitialState() {
        try {
            // Clean up orphaned temp files on startup
            await cleanupOrphanedTempFiles(15); // Clean files older than 15 minutes

            const { requests, settings, sortBy, uploadState } = await chrome.storage.local.get({
                requests: [],
                settings: { extremeClean: false, keepData: false },
                sortBy: 'useCount',
                uploadState: null
            });

            state.requests = requests || [];`);

console.log('Fixed loadInitialState function structure');

// Save the corrected file
fs.writeFileSync(filePath, content, 'utf8');
console.log('✅ Restored basic structure - manual fixes may still be needed');