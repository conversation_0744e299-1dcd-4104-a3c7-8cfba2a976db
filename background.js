importScripts('shared.js');

// =================================================================================
// Helper Functions
// =================================================================================

/**
 * Extracts the first valid JSON object or array from a larger string.
 * This version is enhanced to handle JSON wrapped in markdown code blocks and ignore trailing text.
 * @param {string} text The text to search within.
 * @returns {string} The extracted JSON string, or an empty string if not found.
 */
function extractJson(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    // Handle markdown code blocks (e.g., ```json ... ``` or ``` ... ```)
    const markdownMatch = text.match(/```(json)?\s*([\s\S]*?)\s*```/);
    if (markdownMatch && markdownMatch[2]) {
        text = markdownMatch[2].trim();
    }

    const firstBracket = text.indexOf('{');
    const firstSquare = text.indexOf('[');

    let start = -1;

    if (firstBracket === -1 && firstSquare === -1) {
        return ''; // No JSON found
    }
    if (firstBracket === -1) {
        start = firstSquare;
    } else if (firstSquare === -1) {
        start = firstBracket;
    } else {
        start = Math.min(firstBracket, firstSquare);
    }

    const startChar = text[start];
    const endChar = startChar === '{' ? '}' : ']';

    let balance = 0;
    let end = -1;

    for (let i = start; i < text.length; i++) {
        if (text[i] === startChar) {
            balance++;
        } else if (text[i] === endChar) {
            balance--;
        }

        if (balance === 0) {
            end = i;
            break; // Found the end of the first complete JSON object/array
        }
    }

    if (end !== -1) {
        const jsonOnly = text.substring(start, end + 1);
        return jsonOnly;
    }

    return ''; // Unbalanced JSON, return empty
}


// =================================================================================
// Notification System
// =================================================================================

function showAllNotifications(message, type, tabId) {
    showSystemNotification(message, type);
    showBadge(type === 'success' ? '✔' : '✖', type === 'success' ? '#28a745' : '#dc3545');
    if (tabId) {
        showInPageNotification(message, type, tabId);
    }
}

function showSystemNotification(message, type) {
    chrome.notifications.create({
        type: 'basic',
        iconUrl: type === 'success' ? 'icon-success.png' : 'icon-error.png',
        title: type === 'success' ? 'Success' : 'Error',
        message: message
    });
}

function showBadge(text, color) {
    chrome.action.setBadgeText({ text });
    chrome.action.setBadgeBackgroundColor({ color });
    setTimeout(() => {
        chrome.action.setBadgeText({ text: '' });
    }, 3000);
}

async function showInPageNotification(message, type, tabId) {
    try {
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content.js'],
        });
        await chrome.tabs.sendMessage(tabId, {
            action: "showInPageNotification",
            message: message,
            type: type
        });
    } catch (err) {
        // Errors are handled by the caller, so no need to log here.
    }
}

// =================================================================================
// Context Menu Logic
// =================================================================================

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

const debouncedUpdateContextMenu = debounce(updateContextMenu, 150);

let isUpdatingContextMenu = false;
async function updateContextMenu() {
    if (isUpdatingContextMenu) return;
    isUpdatingContextMenu = true;

    try {
        // Fetch the latest requests from storage.
        const { requests } = await chrome.storage.local.get({ requests: [] });

        // Always remove all menus first to ensure a clean slate.
        await new Promise(resolve => chrome.contextMenus.removeAll(resolve));

        // Filter for requests that are valid for the context menu.
        const menuRequests = requests.filter(r => r.isPinned);

        if (menuRequests.length > 0) {
            // Create the parent menu item.
            chrome.contextMenus.create({
                id: "sendToParent",
                title: "Send to Request",
                contexts: ["selection", "link", "image", "page"]
            }, () => {
                if (chrome.runtime.lastError) { /* Silently handle error */ }
            });

            // Create a sub-menu item for each valid request.
            menuRequests.forEach(request => {
                const title = request.extremeClean ? `${request.name} 🧹` : request.name;
                chrome.contextMenus.create({
                    id: request.id,
                    parentId: "sendToParent",
                    title: title,
                    contexts: ["selection", "link", "image", "page"]
                }, () => {
                    if (chrome.runtime.lastError) { /* Silently handle error */ }
                });
            });
        }
    } finally {
        isUpdatingContextMenu = false;
    }
}

async function handleContextMenuClick(info, tab) {
    const requestId = info.menuItemId;
    if (requestId === "sendToParent") return;

    try {
        // Get all necessary data from storage
        const { requests } = await chrome.storage.local.get({
            requests: []
        });
        const request = requests.find(r => r.id === requestId);
        if (!request) throw new Error(`Could not find request with ID ${requestId}`);

        // Determine the primary data source
        let dataToProcess = null;
        let dataSource = 'page context';

        if (info.selectionText) {
            dataToProcess = info.selectionText;
            dataSource = 'selection';
        } else {
            const clipboardResponse = await chrome.tabs.sendMessage(tab.id, { action: 'getClipboard' });
            if (clipboardResponse && clipboardResponse.success && clipboardResponse.text) {
                dataToProcess = clipboardResponse.text;
                dataSource = 'clipboard';
            }
        }

        // Prepare the body based on the data source and cleaning settings
        let body;
        let wasCleaned = false;

        if (dataToProcess) {
            let textToParse = dataToProcess;
            if (request.extremeClean) {
                textToParse = extractJson(dataToProcess);
                if (textToParse !== dataToProcess) {
                    wasCleaned = true;
                }
            }

            try {
                // Only attempt to parse the cleaned or original text, no repair.
                const parsedJson = JSON.parse(textToParse);
                body = JSON.stringify(parsedJson);
            } catch (e) {
                const firstItemWithKey = request.simpleBody.find(item => item.key);
                if (!firstItemWithKey) {
                    throw new Error(`The request "${request.name}" has no keys defined. Cannot send raw text.`);
                }
                const bodyObject = { [firstItemWithKey.key]: dataToProcess };
                body = JSON.stringify(bodyObject);
            }
        } else {
            const fallbackData = info.linkUrl || info.srcUrl || info.pageUrl;
            if (!fallbackData) throw new Error('No data to send.');

            const firstItemWithKey = request.simpleBody.find(item => item.key);
            if (!firstItemWithKey) {
                throw new Error(`The request "${request.name}" has no keys defined. Cannot send URL.`);
            }
            const bodyObject = { [firstItemWithKey.key]: fallbackData };
            body = JSON.stringify(bodyObject);
        }

        // Use the centralized function to execute the request and handle state
        await executeAndProcessRequest({
            request,
            body,
            requests,
            // Note: per-request settings are used, not global settings
            settings: { keepData: !!request.keepData, extremeClean: !!request.extremeClean },
            wasCleaned,
            source: `Context Menu (${dataSource})`,
            tabId: tab.id
        });

    } catch (error) {
        showAllNotifications(`Error: ${error.message}`, 'error', tab.id);
    }
}

// =================================================================================
// Event Listeners
// =================================================================================

// Build the context menu when the extension is installed or the browser starts.
chrome.runtime.onInstalled.addListener(updateContextMenu);
chrome.runtime.onStartup.addListener(updateContextMenu);

// The primary, reliable trigger for menu updates.
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && (changes.requests || changes.settings)) {
        debouncedUpdateContextMenu();
    }
});

// Listen for clicks on the context menu items.
chrome.contextMenus.onClicked.addListener(handleContextMenuClick);

// Progress tracking for large file uploads
const uploadProgress = new Map();

// Storage cleanup functions
async function cleanupOrphanedTempFiles(maxAgeMinutes = 30) {
    try {
        const allItems = await chrome.storage.local.get(null);
        const tempFileKeys = Object.keys(allItems).filter(key => key.startsWith('temp_files_'));
        const currentTime = Date.now();
        const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
        let cleanedCount = 0;

        console.log(`Background cleanup: Found ${tempFileKeys.length} temporary file entries`);

        for (const key of tempFileKeys) {
            // Extract timestamp from key (temp_files_timestamp)
            const timestampStr = key.replace('temp_files_', '');
            const timestamp = parseInt(timestampStr, 10);

            if (!isNaN(timestamp)) {
                const age = currentTime - timestamp;
                if (age > maxAge) {
                    await chrome.storage.local.remove(key);
                    cleanedCount++;
                    console.log(`Background cleanup: Removed orphaned temp file: ${key} (age: ${Math.round(age / 60000)} minutes)`);
                }
            } else {
                // Invalid timestamp, clean it up
                await chrome.storage.local.remove(key);
                cleanedCount++;
                console.log(`Background cleanup: Removed invalid temp file key: ${key}`);
            }
        }

        if (cleanedCount > 0) {
            console.log(`Background storage cleanup: Removed ${cleanedCount} orphaned temporary files`);
        }

        return cleanedCount;
    } catch (error) {
        console.error('Background cleanup failed:', error);
        return 0;
    }
}

// Run cleanup on extension startup
chrome.runtime.onStartup.addListener(() => {
    console.log('Extension started, performing storage cleanup...');
    cleanupOrphanedTempFiles(10); // Clean files older than 10 minutes on startup
});

// Run cleanup when extension is installed
chrome.runtime.onInstalled.addListener(() => {
    console.log('Extension installed/updated, performing storage cleanup...');
    cleanupOrphanedTempFiles(5); // Clean files older than 5 minutes on install
});

// Periodic cleanup - run every 15 minutes
chrome.alarms.create('storage-cleanup', { periodInMinutes: 15 });
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'storage-cleanup') {
        console.log('Running periodic storage cleanup...');
        cleanupOrphanedTempFiles(20); // Clean files older than 20 minutes
    }
});

// Listen for messages from other parts of the extension.
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.action) {
        case 'testUrl':
            fetch(message.url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: "This is a test from Multi Webhook Sender" })
            })
                .then(response => sendResponse({ status: response.status, statusText: response.statusText, ok: response.ok }))
                .catch(error => sendResponse({ error: error.message, ok: false }));
            return true;

        case 'getProgress':
            const progress = uploadProgress.get(message.requestId);
            sendResponse({ progress: progress || null });
            return false;

        case 'executeRequest':
            (async () => {
                const requestId = message.requestId || Date.now().toString();

                const { requests } = await chrome.storage.local.get({
                    requests: []
                });

                // Reconstruct the full request from safe message format
                let requestFromPopup = { ...message.requestData };

                // Restore file data from temporary storage if needed
                if (message._hasLargeFiles) {
                    const tempFileKey = `temp_files_${requestId}`;
                    let tempFileData;

                    try {
                        tempFileData = await chrome.storage.local.get(tempFileKey);
                    } catch (error) {
                        console.error('Failed to retrieve temp file data:', error);
                        sendResponse({ success: false, error: 'Failed to retrieve file data' });
                        return;
                    }

                    if (tempFileData[tempFileKey]) {
                        const originalFormDataFiles = tempFileData[tempFileKey];
                        console.log(`Retrieved ${originalFormDataFiles.length} files from temporary storage`);

                        requestFromPopup.formDataBody = requestFromPopup.formDataBody.map(item => {
                            if (item._isFileRef) {
                                const fileData = originalFormDataFiles.find(f => f.index === item._originalIndex);
                                if (fileData) {
                                    return {
                                        ...item,
                                        value: fileData.data,
                                        fileName: fileData.fileName,
                                        key: fileData.key,
                                        _isFileRef: undefined,
                                        _originalIndex: undefined
                                    };
                                }
                            }
                            return item;
                        });

                        // Clean up temporary storage - use try/catch to ensure cleanup always happens
                        try {
                            await chrome.storage.local.remove(tempFileKey);
                            console.log(`Cleaned up temporary storage: ${tempFileKey}`);
                        } catch (cleanupError) {
                            console.error('Failed to cleanup temp storage:', cleanupError);
                            // Continue anyway - cleanup failure shouldn't stop the request
                        }

                        console.log('Reconstructed file data from temporary storage');
                    } else {
                        console.warn(`No temp file data found for key: ${tempFileKey}`);
                        sendResponse({ success: false, error: 'File data not found - upload may have expired' });
                        return;
                    }
                }

                // Create progress callback for this request
                const progressCallback = (progressData) => {
                    uploadProgress.set(requestId, progressData);
                };

                const response = await executeAndProcessRequest({
                    request: requestFromPopup,
                    body: null, // Body is part of the request object for popups
                    requests,
                    // Use per-request settings shipped from popup
                    settings: { keepData: !!requestFromPopup.keepData, extremeClean: !!requestFromPopup.extremeClean },
                    source: 'Popup',
                    tabId: sender.tab ? sender.tab.id : null,
                    progressCallback: progressCallback
                });

                // Clear progress after completion
                uploadProgress.delete(requestId);

                // Include request ID in response
                response.requestId = requestId;
                sendResponse(response);
            })();
            return true;
    }
});


/**
 * The centralized function for executing a request and handling all related state updates.
 */
async function executeAndProcessRequest({ request, body, requests, settings, source, tabId, wasCleaned = false, progressCallback = null }) {
    let finalBody;
    let bodyForLog;

    if (source === 'Popup') {
        bodyForLog = request.body;

        // --- GUARD CLAUSE: Check for invalid JSON when cleaning is OFF ---
        if (!settings.extremeClean) {
            if (request.bodyType === 'raw') {
                const contentTypeHeader = request.headers.find(h => h.key.toLowerCase() === 'content-type');
                const isJsonContent = contentTypeHeader && contentTypeHeader.value.toLowerCase().includes('json');
                if (isJsonContent && request.body.trim()) {
                    try {
                        JSON.parse(request.body);
                    } catch (e) {
                        const errorMsg = "Raw body contains invalid JSON and Clean JSON is disabled.";
                        showAllNotifications(errorMsg, 'error', tabId);
                        return { error: errorMsg, isSuccess: false };
                    }
                }
            } else if (request.bodyType === 'json') { // Simple Mode
                for (const item of request.simpleBody) {
                    const valueStr = item.value.trim();
                    if ((valueStr.startsWith('{') && valueStr.endsWith('}')) || (valueStr.startsWith('[') && valueStr.endsWith(']'))) {
                        try {
                            JSON.parse(valueStr);
                        } catch (e) {
                            const errorMsg = `Value for key "${item.key}" is invalid JSON and Clean JSON is disabled.`;
                            showAllNotifications(errorMsg, 'error', tabId);
                            return { error: errorMsg, isSuccess: false };
                        }
                    }
                }
            }
        }
        // --- END GUARD CLAUSE ---


        if (request.bodyType === 'raw') {
            const contentTypeHeader = request.headers.find(h => h.key.toLowerCase() === 'content-type');
            const isJsonContent = contentTypeHeader && contentTypeHeader.value.toLowerCase().includes('json');

            if (isJsonContent) {
                let textToParse = request.body;
                if (textToParse.trim() === '') {
                    finalBody = '{}';
                } else {
                    if (settings.extremeClean) {
                        textToParse = extractJson(textToParse);
                    }
                    finalBody = JSON.stringify(JSON.parse(textToParse));
                }
            } else {
                finalBody = request.body; // Send as plain text
            }
        } else if (request.bodyType === 'form-data') {
            finalBody = new FormData();

            for (const item of request.formDataBody) {
                if (!item || !item.key) continue;

                try {
                    if (item.type === 'text') {
                        finalBody.append(item.key, item.value ?? '');
                    } else if (item.type === 'file' && item.value) {
                        // Support both dataURL and blob URL inputs
                        let blob;
                        if (typeof item.value === 'string' && item.value.startsWith('data:')) {
                            // data URL -> Blob
                            const res = await fetch(item.value);
                            blob = await res.blob();
                        } else if (typeof item.value === 'string' && item.value.startsWith('blob:')) {
                            // blob URL -> Blob
                            const res = await fetch(item.value);
                            blob = await res.blob();
                        } else {
                            // As a fallback, attempt to treat it as a URL (may fail for http/https due to CORS)
                            const res = await fetch(item.value);
                            blob = await res.blob();
                        }

                        const filename = item.fileName || 'unnamed.bin';
                        finalBody.append(item.key, blob, filename);
                    }
                } catch (fileErr) {
                    showAllNotifications(`File handling failed for key "${item?.key}": ${fileErr?.message || fileErr}`, 'error', tabId);
                    return { error: `File handling failed for key "${item?.key}": ${fileErr?.message || fileErr}`, isSuccess: false };
                }
            }

            bodyForLog = '[FormData object]';
        } else { // Simple (Key-Value)
            const simpleBodyObject = request.simpleBody.reduce((obj, item) => {
                if (item.key) obj[item.key] = item.value;
                return obj;
            }, {});
            const unflattened = unflattenObject(simpleBodyObject);
            for (const key in unflattened) {
                if (typeof unflattened[key] === 'string') {
                    let valueToParse = unflattened[key];
                    if (settings.extremeClean) {
                        valueToParse = extractJson(valueToParse);
                    }
                    try {
                        unflattened[key] = JSON.parse(valueToParse);
                    } catch (e) {
                        unflattened[key] = unflattened[key];
                    }
                }
            }
            finalBody = JSON.stringify(unflattened, null, 2);
        }
    } else { // Context Menu
        finalBody = body;
        bodyForLog = body;
    }

    const requestDataForFetch = {
        method: request.method,
        url: request.url,
        headers: request.headers,
        body: finalBody,
        bodyType: request.bodyType
    };

    const response = await executeRequest(requestDataForFetch, progressCallback);

    // Pass through enhanced response data
    response.bodyType = request.bodyType;
    response.requestSize = response.requestSize || 0;
    response.hasLargeFile = response.hasLargeFile || false;

    if (response.isSuccess) {
        const requestToUpdate = requests.find(r => r.id === request.id);
        if (requestToUpdate) {
            // First, update the stored request with the fresh data from the popup if applicable
            if (source === 'Popup') {
                Object.assign(requestToUpdate, request);
            }

            // Then, update the metadata
            requestToUpdate.useCount = (requestToUpdate.useCount || 0) + 1;
            requestToUpdate.lastUsed = Date.now();

            // Finally, apply the data clearing logic to the now-updated request object
            if (!settings.keepData) {
                if (requestToUpdate.bodyType === 'raw') {
                    requestToUpdate.body = '';
                } else if (requestToUpdate.bodyType === 'json') {
                    // Iterate over each item and clear its value, preserving the key.
                    requestToUpdate.simpleBody.forEach(item => {
                        item.value = '';
                    });
                    // If the simpleBody is now completely empty, add a default blank row.
                    if (requestToUpdate.simpleBody.length === 0) {
                        requestToUpdate.simpleBody.push({ key: '', value: '' });
                    }
                } else if (requestToUpdate.bodyType === 'form-data') {
                    requestToUpdate.formDataBody.forEach(item => {
                        item.value = '';
                        if (item.type === 'file') item.fileName = '';
                    });
                }
            }
        }

        await chrome.storage.local.set({ requests });

        const successMessage = wasCleaned ?
            `JSON cleaned and sent to: ${request.name}` :
            `Successfully sent to: ${request.name}`;
        showAllNotifications(successMessage, 'success', tabId);

    } else {
        // Human-friendly error translation with code in brackets
        let humanMessage = '';
        if (response.status === 429) {
            humanMessage = "You are sending requests too quickly. Please wait a moment and try again. [429 Too Many Requests]";
        } else if (response.status === 400) {
            humanMessage = "The server says your request is invalid. Check the URL, headers, or body format. [400 Bad Request]";
        } else if (response.status === 401) {
            humanMessage = "Authorization failed. Check your API key or Authorization header. [401 Unauthorized]";
        } else if (response.status === 403) {
            humanMessage = "You don't have permission to access this resource. [403 Forbidden]";
        } else if (response.status === 404) {
            humanMessage = "The endpoint URL was not found. Verify the path. [404 Not Found]";
        } else if (response.status === 405) {
            humanMessage = "The HTTP method is not allowed for this endpoint. Try a different method. [405 Method Not Allowed]";
        } else if (response.status === 413) {
            humanMessage = response.hasLargeFile ?
                "The file you're sending is too large for this webhook endpoint. Try a smaller file or compress it. [413 Payload Too Large]" :
                "The request body is too large for the server to accept. [413 Payload Too Large]";
        } else if (response.status === 415) {
            humanMessage = "The server doesn't support the Content-Type you're sending. [415 Unsupported Media Type]";
        } else if (response.status === 422) {
            humanMessage = "The server couldn't process the fields you sent. Check your payload structure. [422 Unprocessable Entity]";
        } else if (response.status === 500) {
            humanMessage = "The server had an internal error. Try again later. [500 Internal Server Error]";
        } else if (response.status === 502) {
            humanMessage = "Bad gateway response from upstream server. Try again. [502 Bad Gateway]";
        } else if (response.status === 503) {
            humanMessage = "Service is temporarily unavailable. Try again later. [503 Service Unavailable]";
        } else if (response.status === 504) {
            humanMessage = "The server took too long to respond. Try again. [504 Gateway Timeout]";
        } else if (response.error) {
            // Network/transport errors from fetch wrapper
            humanMessage = "Network or browser blocked the request. Check your internet, CORS, or the endpoint. [Fetch Error]";
        } else {
            humanMessage = `Request failed with unexpected status. [${response.status || 'Unknown'}]`;
        }

        showAllNotifications(humanMessage, 'error', tabId);
    }

    // Add the final state of the request to the response object so the popup can update instantly.
    const finalRequestState = requests.find(r => r.id === request.id);
    if (finalRequestState) {
        response.updatedRequest = finalRequestState;
    }

    return response;
}
