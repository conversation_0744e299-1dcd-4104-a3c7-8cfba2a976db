// Quick fix script for popup.js syntax error
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'popup.js');

try {
    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');

    // Find and fix the missing closing bracket for setupKeyboardShortcuts function
    const searchPattern = '                });\n\n            // Storage cleanup button event listener';
    const replacement = '                });\n            }\n\n            // Storage cleanup button event listener';

    if (content.includes(searchPattern)) {
        content = content.replace(searchPattern, replacement);

        // Write back to file
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('✅ Successfully fixed the missing closing bracket in setupKeyboardShortcuts function!');
        console.log('🎉 Theme switching and body type switching should now work correctly.');
        console.log('📝 Added missing "}" after the addEventListener in setupKeyboardShortcuts.');
    } else {
        console.log('❌ Target pattern not found. Let me try a different approach...');

        // Alternative approach - look for the function and add bracket before storage cleanup
        if (content.includes('// Storage cleanup button event listener')) {
            const lines = content.split('\n');
            let fixedLines = [];
            let foundStorageCleanup = false;

            for (let i = 0; i < lines.length; i++) {
                if (lines[i].includes('// Storage cleanup button event listener') && !foundStorageCleanup) {
                    // Add the missing bracket before this line
                    fixedLines.push('            }');
                    fixedLines.push('');
                    foundStorageCleanup = true;
                }
                fixedLines.push(lines[i]);
            }

            if (foundStorageCleanup) {
                fs.writeFileSync(filePath, fixedLines.join('\n'), 'utf8');
                console.log('✅ Successfully added missing bracket using alternative method!');
                console.log('🎉 Theme switching and body type switching should now work correctly.');
            } else {
                console.log('❌ Could not find storage cleanup comment for insertion point.');
            }
        }
    }

} catch (error) {
    console.error('❌ Error fixing popup.js:', error.message);
}