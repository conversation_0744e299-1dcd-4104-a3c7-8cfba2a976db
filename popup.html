<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi Webhook Sender</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <div class="panel-left" id="panel-left">
            <div class="list-header">
                <h3>Saved Requests</h3>
                <div class="header-actions">
                    <button id="sort-btn" class="header-btn sort-btn">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                            <path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z" />
                        </svg>
                    </button>
                    <button id="theme-toggle-btn" class="header-btn theme-btn" title="Switch to Dark Mode">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                            <path
                                d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z" />
                        </svg>
                    </button>
                    <button id="add-new-request-btn" class="header-btn add-btn">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="search-wrapper">
                <input type="text" id="search-input" placeholder="Search requests...">
            </div>
            <div id="sort-menu" class="sort-menu hidden">
                <div class="sort-option" data-value="name">Name (A-Z)</div>
                <div class="sort-option" data-value="createdAt">Date Created</div>
                <div class="sort-option" data-value="useCount">Most Used</div>
            </div>
            <ul id="requests-list" class="requests-list">
                <!-- Request items will be dynamically added here -->
            </ul>
            <div class="list-footer">
                <button id="delete-request-btn" class="delete-btn full-width">Delete Selected</button>
                <div class="settings-section">
                    <h5>Save Settings</h5>
                    <div class="import-export-buttons">
                        <button id="export-btn" class="io-btn">↑ Export</button>
                        <label for="import-file" class="io-btn">↓ Import</label>
                        <input type="file" id="import-file" accept=".json" style="display: none;">
                    </div>
                    <div class="storage-cleanup-section">
                        <h5>🛠️ Maintenance</h5>
                        <button id="storage-cleanup-btn" class="cleanup-btn"
                            title="Clear temporary files and free storage space">
                            🧹 Clean Storage
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="resize-handle" id="resize-handle"></div>
        <div class="panel-right" id="panel-right">
            <div id="request-editor" class="request-editor" tabindex="-1">
                <div class="editor-header">
                    <div class="request-name-wrapper">
                        <input type="text" id="request-name" placeholder="Request Name" class="request-name-input">
                        <button id="save-name-btn" class="save-name-btn hidden">Save</button>
                    </div>
                    <div class="editor-header-actions">
                        <button id="fullscreen-btn" class="header-btn fullscreen-btn" title="Toggle Full-Screen Mode">
                            <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                                <path
                                    d="M5,5H10V7H7V10H5V5M14,5H19V10H17V7H14V5M17,14H19V19H14V17H17V14M10,17V19H5V14H7V17H10Z" />
                            </svg>
                        </button>
                        <div class="body-type-switcher">
                            <label for="body-type-select">Body Type</label>
                            <select id="body-type-select">
                                <option value="json">JSON (Key-Value)</option>
                                <option value="raw">Raw Text</option>
                                <option value="form-data">Form-Data (Files)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="notification-bar" class="notification-bar hidden">
                    <span id="notification-message"></span>
                    <div id="notification-actions"></div>
                </div>

                <div class="editor-content">
                    <div class="request-form">
                        <div class="form-group url-group">
                            <label for="url">URL</label>
                            <div class="url-input-wrapper">
                                <input type="text" id="url" placeholder="https://api.example.com/data">
                                <button id="test-url-btn" class="test-btn">Test</button>
                            </div>
                        </div>

                        <div id="advanced-options" class="advanced-options hidden">
                            <div class="form-group">
                                <label for="http-method">Method</label>
                                <select id="http-method">
                                    <option>POST</option>
                                    <option>GET</option>
                                    <option>PUT</option>
                                    <option>DELETE</option>
                                </select>
                            </div>
                            <div class="headers-section">
                                <div class="headers-header">
                                    <h4>Headers</h4>
                                    <button id="add-header-btn" class="add-btn-small">
                                        <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                        </svg>
                                    </button>
                                </div>
                                <div id="headers-container">
                                    <!-- Header key-value pairs will be added here -->
                                </div>
                            </div>
                        </div>

                        <div id="simple-body-editor" class="body-section hidden">
                            <div class="body-header">
                                <h4>Body (Key-Value)</h4>
                                <button id="add-simple-row-btn" class="add-btn-small">
                                    <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                    </svg>
                                </button>
                            </div>
                            <div id="simple-body-container">
                                <!-- Simple body key-value pairs will be added here -->
                            </div>
                        </div>
                        <div id="form-data-editor" class="body-section hidden">
                            <div class="body-header">
                                <h4>Body (Form-Data)</h4>
                                <button id="add-form-data-row-btn" class="add-btn-small">
                                    <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                                    </svg>
                                </button>
                            </div>
                            <div id="form-data-container">
                                <!-- Form-data key-value/file pairs will be added here -->
                            </div>
                        </div>
                        <div id="value-char-word-count" class="char-word-count hidden">
                            <span id="char-count">0</span> characters, <span id="word-count">0</span> words
                        </div>

                        <div id="advanced-body-editor" class="body-section hidden">
                            <div class="body-header">
                                <h4>Body (Raw)</h4>
                                <button id="clear-raw-body-btn" class="icon-btn" title="Clear raw body">
                                    <svg viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
                                        <path
                                            d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                                    </svg>
                                </button>
                            </div>
                            <textarea id="body-input" rows="4" placeholder="Enter raw request body here..."></textarea>
                        </div>

                        <div id="response-viewer" class="response-viewer hidden">
                            <div class="response-header">
                                <h4>Response</h4>
                                <span id="response-status"></span>
                            </div>
                            <pre id="response-body"></pre>
                        </div>

                        <div class="footer-actions">
                            <div class="keep-data-wrapper">
                                <input type="checkbox" id="keep-data-checkbox">
                                <label for="keep-data-checkbox">Keep data</label>
                            </div>
                            <div class="keep-data-wrapper">
                                <input type="checkbox" id="extreme-clean-checkbox">
                                <label for="extreme-clean-checkbox">Clean JSON</label>
                            </div>
                            <button id="send-btn" class="send-btn">
                                <span class="btn-text">Send</span>
                            </button>
                        </div>
                        <div class="footer-links">
                            <a href="https://www.youtube.com/@softreviewed?sub_confirmation=1" target="_blank"
                                class="footer-link footer-link-icon" id="youtube-link">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14"
                                    fill="currentColor">
                                    <path
                                        d="M10,15L15.19,12L10,9V15M21.56,7.17C21.69,7.64 21.78,8.27 21.84,9.07C21.91,9.87 21.94,10.56 21.94,11.16L22,12C22,14.19 21.84,15.8 21.56,16.83C21.31,17.73 20.73,18.31 19.83,18.56C19.36,18.69 18.73,18.78 17.93,18.84C17.13,18.91 16.44,18.94 15.84,18.94L15,19C12.81,19 11.2,18.84 10.17,18.56C9.27,18.31 8.69,17.73 8.44,16.83C8.31,16.36 8.22,15.73 8.16,14.93C8.09,14.13 8.06,13.44 8.06,12.84L8,12C8,9.81 8.16,8.2 8.44,7.17C8.69,6.27 9.27,5.69 10.17,5.44C11.2,5.16 12.81,5 15,5L15.84,5.06C16.44,5.06 17.13,5.09 17.93,5.16C18.73,5.22 19.36,5.31 19.83,5.44C20.73,5.69 21.31,6.27 21.56,7.17Z" />
                                </svg>
                                <span>YouTube</span>
                            </a>
                            <a href="https://buymeacoffee.com/jovin2f" target="_blank"
                                class="footer-link footer-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14"
                                    fill="currentColor">
                                    <path
                                        d="M2,21H20V19H2M20,8H4V13H14V11H18V13H20V11H22V9H20V5H18V3H16V5H4A2,2 0 0,0 2,7V8H4M4,7H14V8H4V7Z" />
                                </svg>
                                <span>Donate</span>
                            </a>
                            <a href="https://chromewebstore.google.com/detail/multi-webhook-sender/kbkfglmdbbkppnmojdmpndadegmlhhkb"
                                target="_blank" class="footer-link footer-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14"
                                    fill="currentColor">
                                    <path
                                        d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                                </svg>
                                <span>Rate Us</span>
                            </a>
                            <a href="https://softreviewed.com/multi-service-webhook-sender/" target="_blank"
                                class="footer-link footer-link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14"
                                    fill="currentColor">
                                    <path
                                        d="M19,2H5A2,2 0 0,0 3,4V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V4A2,2 0 0,0 19,2M19,20H5V4H7V12L9.5,10.5L12,12V4H19V20Z" />
                                </svg>
                                <span>Learn More</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script src="shared.js"></script>
    <script src="popup.js"></script>
</body>

</html>