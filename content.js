// Listen for messages from the background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "showInPageNotification") {
        showInPageNotification(request.message, request.type);
    } else if (request.action === "getClipboard") {
        // The background script is asking for the clipboard content.
        navigator.clipboard.readText()
            .then(text => sendResponse({ success: true, text: text }))
            .catch(err => sendResponse({ success: false, error: err.message }));
        return true; // Indicates we will send a response asynchronously.
    }
});

function showInPageNotification(message, type) {
    const notification = document.createElement('div');
    notification.textContent = message;

    // Basic styling
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.padding = '15px 20px';
    notification.style.borderRadius = '5px';
    notification.style.color = 'white';
    notification.style.zIndex = '999999';
    notification.style.fontFamily = 'sans-serif';
    notification.style.fontSize = '16px';
    notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';

    // Type-specific styling
    if (type === 'success') {
        notification.style.backgroundColor = '#28a745'; // Green
    } else {
        notification.style.backgroundColor = '#dc3545'; // Red
    }

    document.body.appendChild(notification);

    // Remove the notification after a few seconds
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 3000);
}
