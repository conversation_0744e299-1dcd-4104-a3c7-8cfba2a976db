<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Progress & Filename Demo - Multi Webhook Sender</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            height: auto;
            max-width: 800px;
            margin: 0 auto;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--panel-background);
        }

        .demo-section h2 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .demo-section p {
            color: var(--light-text-color);
            line-height: 1.5;
        }

        .form-data-item {
            margin-bottom: 20px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li:before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
            font-size: 16px;
        }

        .demo-button {
            margin: 10px 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }

        .demo-send-normal {
            background-color: var(--primary-color);
            color: white;
        }

        .demo-send-progress {
            background-color: #f39c12;
            color: white;
            position: relative;
        }

        .demo-send-success {
            background-color: var(--success-color);
            color: white;
        }

        .demo-send-error {
            background-color: var(--error-color);
            color: white;
        }

        .filename-demo {
            font-family: inherit;
            font-size: 13px;
            color: var(--text-color);
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            gap: 2px;
            max-width: 200px;
            border: 1px solid var(--border-color);
            padding: 4px 8px;
            border-radius: 4px;
            margin: 8px 0;
        }

        .filename-start {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex-shrink: 1;
            min-width: 0;
        }

        .filename-ext {
            font-weight: 600;
            color: var(--primary-color);
            flex-shrink: 0;
        }
    </style>
</head>

<body>
    <h1>🚀 Enhanced Progress & Filename Display</h1>
    <p>The Multi Webhook Sender now has improved filename display and progress tracking for large file uploads!</p>

    <div class="demo-section">
        <h2>📝 Fixed Filename Display</h2>
        <p><strong>Problem Fixed:</strong> Long filenames now display properly without overflowing containers.</p>

        <h4>Before (Problematic):</h4>
        <p>Filename would overflow and get cut off awkwardly</p>

        <h4>After (Fixed):</h4>
        <div class="filename-demo" title="Robot-Threads-Needle-Gives-Generator-Super-Long-Video-Name.mp4">
            <span class="filename-start">Robot-Threads-Needle-Gives-Generator-Super-Long-Video-Name</span><span
                class="filename-ext">.mp4</span>
        </div>
        <div class="filename-demo" title="My-Awesome-Project-Video-Final-Version-2024.mov">
            <span class="filename-start">My-Awesome-Project-Video-Final-Version-2024</span><span
                class="filename-ext">.mov</span>
        </div>
        <div class="filename-demo" title="Short.avi">
            <span class="filename-start">Short</span><span class="filename-ext">.avi</span>
        </div>

        <ul class="feature-list">
            <li>📏 <strong>Smart Truncation</strong> - Filename base gets ellipsis, extension stays visible</li>
            <li>💡 <strong>Tooltip Support</strong> - Hover to see full filename</li>
            <li>🎯 <strong>Container Respect</strong> - Stays within allocated space</li>
            <li>🎨 <strong>Visual Hierarchy</strong> - Extension highlighted in brand color</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>📊 Enhanced Send Progress</h2>
        <p><strong>New Feature:</strong> Smart progress indication when sending large files to webhooks.</p>

        <h4>Send Button States:</h4>
        <button class="demo-button demo-send-normal">Send</button>
        <span>← Normal state</span><br>

        <button class="demo-button demo-send-progress">
            Sending... <div
                style="display: inline-block; width: 12px; height: 12px; border: 2px solid white; border-top-color: transparent; border-radius: 50%; animation: spin 0.8s linear infinite; margin-left: 6px;">
            </div>
        </button>
        <span>← Large file progress</span><br>

        <button class="demo-button demo-send-success">✔</button>
        <span>← Success</span><br>

        <button class="demo-button demo-send-error">✖</button>
        <span>← Error</span><br>

        <ul class="feature-list">
            <li>🎯 <strong>Smart Detection</strong> - Automatically detects files > 5MB</li>
            <li>🔄 <strong>Visual Feedback</strong> - Spinning indicator for large uploads</li>
            <li>📈 <strong>Size Reporting</strong> - Shows file size in success message</li>
            <li>⚠️ <strong>Enhanced Errors</strong> - Specific messages for timeout/size issues</li>
            <li>🚀 <strong>Success Messages</strong> - Special celebration for large file uploads</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>🎬 Large Video File Example</h2>
        <p>Here's how a large video file (> 10MB) displays with the enhanced system:</p>

        <div class="form-data-item">
            <div class="form-data-controls">
                <select class="form-data-type">
                    <option value="file" selected>File</option>
                </select>
                <textarea class="form-data-key" placeholder="Key" rows="1">video</textarea>
            </div>
            <div class="form-data-value-wrapper">
                <div class="form-data-file-display">
                    <div class="file-info">
                        <div class="file-icon video-file">
                            🎥
                        </div>
                        <div class="file-details">
                            <div class="form-data-filename"
                                title="Robot-Threads-Needle-Gives-Generator-Epic-Video-Final-4K.mp4">
                                <span
                                    class="filename-start">Robot-Threads-Needle-Gives-Generator-Epic-Video-Final-4K</span><span
                                    class="filename-ext">.mp4</span>
                            </div>
                            <div class="file-size">83 MB</div>
                            <div class="file-type-label">📹 Video File</div>
                        </div>
                    </div>
                    <button class="icon-btn clear-file-btn" title="Clear file">✕</button>
                </div>
            </div>
            <button class="icon-btn remove-form-data-row-btn">-</button>
        </div>

        <p><strong>What happens when you send this:</strong></p>
        <ul class="feature-list">
            <li>🔄 Button shows "Sending..." with progress animation</li>
            <li>📊 System monitors upload progress internally</li>
            <li>✅ Success shows "🚀 Large file sent successfully! (83.0 MB)"</li>
            <li>⚠️ Timeout errors get specific large-file messaging</li>
            <li>📝 Response includes detailed file transmission confirmation</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>🎯 Progress Messages</h2>
        <p>Enhanced notification messages based on file size and status:</p>

        <div
            style="margin: 10px 0; padding: 10px; background-color: rgba(46, 204, 113, 0.1); border-left: 3px solid var(--success-color); border-radius: 4px;">
            <strong>Small files:</strong> "Request sent successfully!"
        </div>

        <div
            style="margin: 10px 0; padding: 10px; background-color: rgba(46, 204, 113, 0.1); border-left: 3px solid var(--success-color); border-radius: 4px;">
            <strong>Large files:</strong> "🚀 Large file sent successfully!"
        </div>

        <div
            style="margin: 10px 0; padding: 10px; background-color: rgba(231, 76, 60, 0.1); border-left: 3px solid var(--error-color); border-radius: 4px;">
            <strong>Size error:</strong> "📁 File too large for this webhook endpoint. Try a smaller file or different
            endpoint."
        </div>

        <div
            style="margin: 10px 0; padding: 10px; background-color: rgba(231, 76, 60, 0.1); border-left: 3px solid var(--error-color); border-radius: 4px;">
            <strong>Timeout error:</strong> "⏱️ Large file upload timed out. The file may be too large or connection too
            slow."
        </div>

        <div
            style="margin: 10px 0; padding: 10px; background-color: rgba(231, 76, 60, 0.1); border-left: 3px solid var(--error-color); border-radius: 4px;">
            <strong>Network error:</strong> "🌐 Network error during large file upload. Check your connection and try
            again."
        </div>
    </div>

    <div class="demo-section">
        <h2>🛠️ Technical Improvements</h2>
        <ul class="feature-list">
            <li>📐 <strong>CSS Flexbox</strong> - Better container sizing and overflow handling</li>
            <li>🎯 <strong>Smart Detection</strong> - Automatically identifies files requiring special handling</li>
            <li>📊 <strong>Size Calculation</strong> - Accurate FormData size estimation</li>
            <li>🔄 <strong>Progress States</strong> - Multiple UI states for different upload phases</li>
            <li>💬 <strong>Context-Aware Messages</strong> - Error messages specific to file upload scenarios</li>
            <li>🎨 <strong>Visual Polish</strong> - Smooth animations and state transitions</li>
        </ul>
    </div>

    <script>
        // Demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Enhanced Progress & Filename Demo loaded!');
            console.log('Features demonstrated:');
            console.log('✓ Fixed filename overflow/truncation');
            console.log('✓ Smart progress indication for large files');
            console.log('✓ Enhanced error messaging');
            console.log('✓ Better visual feedback during uploads');
        });
    </script>
</body>

</html>