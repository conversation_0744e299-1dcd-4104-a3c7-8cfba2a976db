const fs = require('fs');
const path = require('path');

console.log('🔧 Comprehensive Popup.js Structure Fix');
console.log('=====================================');

const filePath = path.join(__dirname, 'popup.js');
let content = fs.readFileSync(filePath, 'utf8');

// Step 1: Fix the broken loadInitialState function
console.log('🔨 Step 1: Fixing loadInitialState function...');
const fixedLoadInitialState = `    async function loadInitialState() {
        try {
            // Clean up orphaned temp files on startup
            await cleanupOrphanedTempFiles(15); // Clean files older than 15 minutes

            const { requests, settings, sortBy, uploadState } = await chrome.storage.local.get({
                requests: [],
                settings: { extremeClean: false, keepData: false },
                sortBy: 'useCount',
                uploadState: null
            });

            state.requests = requests || [];
            state.settings = { ...state.settings, ...settings };
            state.sortBy = sortBy || 'useCount';

            // Restore upload state if exists
            if (uploadState && uploadState.isUploading) {
                state.activeUpload = uploadState;
                console.log('Restored active upload state:', uploadState);
                // Check if upload is still active and resume progress tracking
                await resumeProgressTracking();
            }

            // Update UI
            extremeCleanCheckbox.checked = state.settings.extremeClean;
            render();
            if (state.requests.length > 0) {
                selectRequest(state.requests[0].id);
            }
        } catch (error) {
            console.error('Failed to load initial state:', error);
        }
    }`;

// Replace the broken loadInitialState function
content = content.replace(
    /async function loadInitialState\(\) \{[\s\S]*?}\s*(?=\s*(async )?function|\s*\/\/|\s*$)/m,
    fixedLoadInitialState + '\n\n'
);

// Step 2: Fix missing function closures
console.log('🔨 Step 2: Adding missing function closures...');

// List of functions that need proper closures
const functions = [
    'saveUploadState',
    'clearUploadState', 
    'handleUploadComplete',
    'togglePin',
    'duplicateRequest',
    'selectRequest',
    'render',
    'flattenObject',
    'updateState',
    'checkStorageUsage',
    'cleanupOrphanedTempFiles',
    'emergencyStorageCleanup',
    'handleManualStorageCleanup'
];

// Fix each function's closure
functions.forEach(funcName => {
    console.log(`  Fixing ${funcName}...`);
    
    // Pattern to match function definition to the next function or end
    const funcPattern = new RegExp(`(\\s*(?:async )?function ${funcName}\\([^)]*\\)\\s*\\{[\\s\\S]*?)(?=\\s*(?:async )?function|\\s*\\/\\/\\s*Storage cleanup|\\s*\\/\\/\\s*---\\s*Initialization|$)`, 'g');
    
    content = content.replace(funcPattern, (match, funcBody) => {
        // Count braces to ensure proper closure
        const openBraces = (funcBody.match(/\{/g) || []).length;
        const closeBraces = (funcBody.match(/\}/g) || []).length;
        
        if (openBraces > closeBraces) {
            const missing = openBraces - closeBraces;
            console.log(`    Added ${missing} closing brace(s)`);
            return funcBody + '\n' + '    }'.repeat(missing) + '\n';
        }
        return funcBody + '\n';
    });
});

// Step 3: Ensure proper DOMContentLoaded closure
console.log('🔨 Step 3: Ensuring DOMContentLoaded closure...');

// The file should end with the initialization section and a single });
const properEnding = `
            // Storage cleanup button event listener
            if (storageCleanupBtn) {
                storageCleanupBtn.onclick = () => {
                    handleManualStorageCleanup();
                };
            }

            // --- Initialization ---
            initializeTheme();
            loadInitialState();
            setupResizablePanels();
            setupFullScreenMode();
            setupKeyboardShortcuts();
        });`;

// Remove any existing ending and add the proper one
content = content.replace(/\/\/ Storage cleanup[\s\S]*$/, properEnding);

// Step 4: Clean up any duplicate lines or malformed structures
console.log('🔨 Step 4: Cleaning up structure...');

// Remove duplicate empty lines
content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

// Remove any standalone }); that might be left over
content = content.replace(/^\s*\}\);\s*$/gm, '');

// Add the proper ending if not present
if (!content.trim().endsWith('});')) {
    content = content.trim() + '\n});';
}

// Step 5: Write the fixed content
console.log('🔨 Step 5: Writing fixed content...');
fs.writeFileSync(filePath, content, 'utf8');

console.log('✅ Comprehensive fix completed!');
console.log('📋 Summary:');
console.log('   - Fixed loadInitialState function structure');
console.log('   - Added missing function closures');
console.log('   - Ensured proper DOMContentLoaded closure');
console.log('   - Cleaned up duplicate/malformed structures');
console.log('');
console.log('🧪 Next step: Run syntax validation to verify fixes');